"""
Neo4j Service - Interface for interacting with Neo4j graph database

This module provides a service for connecting to and querying a Neo4j
graph database for fact verification.
"""

import logging
import os
from typing import Dict, List, Any, Optional

from neo4j import GraphDatabase, Session, Driver

from config.config_types import KnowledgeGraphConfig, KnowledgeGraphConnectionConfig
from kg.types import Neo4JEntity

logger = logging.getLogger(__name__)


class Neo4jService:
    """
    Service for interacting with Neo4j graph database.

    Provides methods for:
    - Connecting to Neo4j
    - Querying entities and relationships
    - Importing data
    - Running graph algorithms
    """

    connection_config: KnowledgeGraphConnectionConfig
    driver: Driver

    def __init__(self, config: KnowledgeGraphConfig):
        """
        Initialize the Neo4j service.

        Args:
            config: Neo4j configuration from settings.yaml
        """
        self.connection_config = config.connection

        # Connect to Neo4j
        self._connect()

        logger.info("Neo4j service initialized")

    def _connect(self) -> None:
        """Connect to Neo4j database."""
        uri = self.connection_config.uri
        username = self.connection_config.username
        password = os.environ.get(self.connection_config.password_env)
        database = self.connection_config.database

        try:
            self.driver = GraphDatabase.driver(uri, auth=(username, password))

            # Verify connection
            with self.driver.session(database=database) as session:
                result = session.run("MATCH (n) RETURN count(n) AS count LIMIT 1")
                count = result.single()["count"]
                logger.info(
                    f"Connected to Neo4j at {uri}, database: {database}, node count: {count}"
                )

        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {str(e)}")
            exit(1)

    def close(self) -> None:
        """Close the Neo4j connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j connection closed")

    def get_session(self, database: Optional[str] = None) -> Session:
        """
        Get a Neo4j session.

        Args:
            database: Optional database name (defaults to config value)

        Returns:
            Neo4j session
        """
        if not self.driver:
            raise ConnectionError("Not connected to Neo4j")

        if not database:
            database = self.connection_config.database

        return self.driver.session(database=database)

    def run_query(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
        database: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Run a Cypher query.

        Args:
            query: Cypher query
            params: Query parameters
            database: Optional database name

        Returns:
            List of records as dictionaries
        """
        if not params:
            params = {}

        try:
            with self.get_session(database) as session:
                # Run the query and collect all records before the session closes
                result = session.run(query, params)
                # Convert all records to dictionaries
                records = [record.data() for record in result]
                return records
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}\nQuery: {query}")
            return []

    def get_entities_by_name(
        self, name: str,
        limit: int = 10
    ) -> List[Neo4JEntity]:
        query = """
        MATCH (e)
        WHERE toLower(e.name) = toLower($name) OR toLower(e.name) CONTAINS toLower($name)
        RETURN elementId(e) as node_id, e as entity,
               CASE
                 WHEN toLower(e.name) = toLower($name) THEN 1.0
                 WHEN toLower(e.name) STARTS WITH toLower($name) THEN 0.9
                 WHEN toLower(e.name) CONTAINS toLower($name) THEN 0.8
                 ELSE 0.5
               END AS score
        ORDER BY score DESC
        LIMIT $limit
        """

        try:
            records = self.run_query(query, {"name": name, "limit": limit})
            entities = []

            for record in records:
                score = record["score"]
                entity_id = record["node_id"]
                entity_name = record["entity"]["name"]

                entities.append(
                    Neo4JEntity(
                        id=entity_id,
                        name=entity_name,
                        score=score
                    )
                )

            return entities

        except Exception as e:
            logger.error(f"Error getting entity by name {name}: {str(e)}")
            return []

    def search_entities(self, query_text: str, limit: int = 10) -> List[Neo4JEntity]:
        """
        Search for entities using basic string matching.

        Args:
            query_text: Search query
            limit: Maximum number of results

        Returns:
            List of matching entities
        """
        try:
            # TODO: This score calculation is just a placeholder.
            #       Consider using more advanced scoring or ranking algorithms.
            query = """
            MATCH (e)
            WHERE toLower(e.name) CONTAINS toLower($query_text)
            RETURN elementId(e) as node_id, e as entity,
                   CASE
                     WHEN toLower(e.name) = toLower($query_text) THEN 1.0
                     WHEN toLower(e.name) STARTS WITH toLower($query_text) THEN 0.9
                     WHEN toLower(e.name) CONTAINS toLower($query_text) THEN 0.8
                     ELSE 0.5
                   END AS score
            ORDER BY score DESC
            LIMIT $limit
            """

            # run_query now returns a list of dictionaries
            records = self.run_query(query, {"query_text": query_text, "limit": limit})
            entities = []

            for record in records:
                score = record["score"]
                entity_name = record["entity"]["name"]
                entity_id = record["node_id"]

                entities.append(
                    Neo4JEntity(
                        id=entity_id,
                        name=entity_name,
                        score=score
                    )
                )

            return entities

        except Exception as e:
            logger.error(
                f"Error searching entities with query '{query_text}': {str(e)}",
                exc_info=True
            )
            exit(1)

    def get_relationships(
        self, subject_id: str, object_id: str
    ) -> List[str]:
        """
        Get relationships between two entities.

        Args:
            subject_id: ID of the subject entity
            object_id: ID of the object entity

        Returns:
            List of relationships
        """
        query = """
        MATCH (s)-[r]-(o)
        WHERE elementId(s) = $subject_id AND elementId(o) = $object_id
        RETURN elementId(r) as relationship_id, type(r) as type
        """

        try:
            # run_query now returns a list of dictionaries
            records = self.run_query(
                query, {"subject_id": subject_id, "object_id": object_id}
            )
            relationships = []

            for record in records:
                relationships.append(record["type"])

            return relationships

        except Exception as e:
            logger.error(
                f"Error getting relationships between {subject_id} and {object_id}: {str(e)}"
            )
            return []

    def get_relationships_of_entity(
        self, entity_id: str, limit: int = 10
    ) -> List[str]:
        query = """
        MATCH (s)-[r]-(o)
        WHERE elementId(s) = $entity_id
        RETURN elementId(r) as relationship_id, type(r) as type
        LIMIT $limit
        """

        try:
            # run_query now returns a list of dictionaries
            records = self.run_query(
                query, {"entity_id": entity_id, "limit": limit}
            )
            relationships = []

            for record in records:
                relationships.append(record["type"])

            return relationships

        except Exception as e:
            logger.error(
                f"Error getting relationships of {entity_id}: {str(e)}"
            )
            return []

    def get_all_connections(self, entity_name: str) -> Dict[str, List[str]]:
        """
        Get all possible connections for an entity.

        Args:
            entity_name: Name of the entity

        Returns:
            Dictionary with entity name as key and list of connection types as value
        """
        try:
            # First get the entity by name
            entities = self.get_entities_by_name(entity_name, 10)
            if not entities:
                # Return empty dict if no entities found, this is not an error
                return {}

            connections = {}

            for entity in entities:
                entity_id = entity.id

                # Get all relationships for this entity
                query = """
                MATCH (e)-[r]-(o)
                WHERE elementId(e) = $entity_id
                RETURN type(r) as relation_type, elementId(o) as other_id, o.name as other_name
                """

                records = self.run_query(query, {"entity_id": entity_id})

                for record in records:
                    relation_type = record["relation_type"]
                    if entity_name not in connections:
                        connections[entity_name] = []
                    connections[entity_name].append(relation_type)
                connections[entity_name] = list(set(connections[entity_name]))

            return connections

        except Exception as e:
            logger.error(f"Error getting connections for entity {entity_name}: {str(e)}")
            exit(1)