"""
Configuration types for FactFence.

This module contains all configuration-related type definitions using dataclasses
to ensure type safety and easy access to configuration values.
"""

from dataclasses import dataclass
from typing import Dict, Optional, Literal


@dataclass
class OutputConfig:
    """Configuration for output data."""

    directory: str


@dataclass
class InputConfig:
    claims_file: str


@dataclass
class EntityLinkingConfig:
    """Configuration for entity linking."""

    threshold: float
    use_cache: bool
    cache_path: str


@dataclass
class ImportConfig:
    """Configuration for knowledge graph import."""

    directory: str
    delete_before_import: bool


@dataclass
class KnowledgeGraphConnectionConfig:
    """Configuration for the Neo4j knowledge graph connection."""

    uri: str
    username: str
    password_env: str
    database: str


@dataclass
class KnowledgeGraphImportConfig:
    directory: str
    delete_before_import: bool


@dataclass
class KnowledgeGraphConfig:
    """Configuration for the knowledge graph."""

    type: str  # 'neo4j' or 'dbpedia'
    connection: KnowledgeGraphConnectionConfig
    import_config: KnowledgeGraphImportConfig


@dataclass
class ModelConfig:
    """Configuration for a specific LLM model."""

    provider: str
    model_name: str
    temperature: float
    max_tokens: int
    api_key_env: str
    request_timeout: int
    max_retries: int
    streaming: bool


@dataclass
class ModelsConfig:
    """Configuration for LLM models."""

    selected: str
    available: Dict[str, ModelConfig]

PromptType = Literal["zero_shot", "few_shot", "cot", "entity_filtering"]
PromptStrategy = Literal["zero_shot", "few_shot", "cot"]

@dataclass
class PromptsConfig:
    """Configuration for prompts."""

    templates: Dict[PromptType, str]
    few_shot_examples: Optional[str] = None


@dataclass
class FactExtractionConfig:
    """Configuration for fact extraction."""

    strategy: PromptStrategy
    models: ModelsConfig
    prompts: PromptsConfig


@dataclass
class LoggingConfig:
    """Configuration for logging."""

    level: str
    format: str
    log_dir: str


@dataclass
class FuzzyRetrievalConfig:
    """Configuration for fuzzy retrieval."""

    similarity_threshold: float = 90.0
    skip_second_stage: bool = False
    max_entity_matches: int = 3


@dataclass
class VerifierConfig:
    """Configuration for verifier."""

    strategy: PromptStrategy
    models: ModelsConfig
    prompts: PromptsConfig


@dataclass
class AppConfig:
    """Main application configuration."""

    output: OutputConfig
    input: InputConfig
    knowledge_graph: KnowledgeGraphConfig
    fact_extraction: FactExtractionConfig
    fuzzy_retrieval: FuzzyRetrievalConfig
    verifier: VerifierConfig
    logging: LoggingConfig
