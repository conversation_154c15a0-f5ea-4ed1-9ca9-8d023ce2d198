# Global Configuration for FactFence

# Input and Output configurations
output:
  directory: "data/output"

input:
  claims_file: "data/input/sample_claims.txt"

# Knowledge Graph configurations
knowledge_graph:
  # Type of knowledge graph to use: 'neo4j' or 'dbpedia'
  type: "neo4j"

  # Neo4j connection settings
  connection:
    #uri: "bolt://localhost:7687"
    uri: "neo4j+s://4d64868c.databases.neo4j.io"
    username: "neo4j"
    password_env: "NEO4J_PASSWORD"
    database: "neo4j"

  # Import configurations
  import:
    directory: "data/graph"
    delete_before_import: true

# Fact extraction configurations
fact_extraction:
  strategy: "zero_shot"  # Options: zero_shot, few_shot, cot
  models:
    selected: "gpt"
    available:
      gpt:
        provider: "openai"
        model_name: "gpt-4.1"
        temperature: 0.0
        max_tokens: 1000
        api_key_env: "OPENAI_API_KEY"
        request_timeout: 60
        max_retries: 3
        streaming: false

      claude:
        provider: "anthropic"
        model_name: "claude-2"
        temperature: 0.0
        max_tokens: 1000
        api_key_env: "ANTHROPIC_API_KEY"
        request_timeout: 60
        max_retries: 3
        streaming: false

      hf_inference_mistral:
        provider: "huggingface_inference"
        model_name: "mistralai/Mistral-7B-Instruct-v0.2"
        temperature: 0.1
        max_tokens: 1000
        api_key_env: "HF_API_TOKEN"
        request_timeout: 120
        max_retries: 3
        streaming: false

      hf_inference_llama:
        provider: "huggingface_inference"
        model_name: "meta-llama/Llama-2-7b-chat-hf"
        temperature: 0.1
        max_tokens: 1000
        api_key_env: "HF_API_TOKEN"
        request_timeout: 120
        max_retries: 3
        streaming: false

      hf_inference_falcon:
        provider: "huggingface_inference"
        model_name: "tiiuae/falcon-7b-instruct"
        temperature: 0.1
        max_tokens: 1000
        api_key_env: "HF_API_TOKEN"
        request_timeout: 120
        max_retries: 3
        streaming: false

  prompts:
    templates:
      zero_shot: "prompts/templates/fact_extraction_zero_shot.txt"
      few_shot: "prompts/templates/fact_extraction_few_shot.txt"
      cot: "prompts/templates/fact_extraction_cot.txt"
      entity_filtering: "prompts/templates/entity_filtering.txt"
    few_shot_examples: "prompts/examples/few_shot_examples.json"

# Fuzzy retrieval configurations
fuzzy_retrieval:
  similarity_threshold: 0.5
  skip_second_stage: true
  max_entity_matches: 10
  max_relation_matches: 5

# Verifier configurations
verifier:
  strategy: "zero_shot"
  models:
    selected: "hf_inference_mistral"  # Changed to use HuggingFace Inference API by default
    available:
      gpt:
        provider: "openai"
        model_name: "gpt-4"
        temperature: 0.0
        max_tokens: 1000
        api_key_env: "OPENAI_API_KEY"
        request_timeout: 60
        max_retries: 3
        streaming: false

      claude:
        provider: "anthropic"
        model_name: "claude-2"
        temperature: 0.0
        max_tokens: 1000
        api_key_env: "ANTHROPIC_API_KEY"
        request_timeout: 60
        max_retries: 3
        streaming: false

      hf_inference_mistral:
        provider: "huggingface_inference"
        model_name: "mistralai/Mistral-7B-Instruct-v0.2"
        temperature: 0.1
        max_tokens: 1000
        api_key_env: "HF_API_TOKEN"  # Optional for some models
        request_timeout: 120
        max_retries: 3
        streaming: false

      hf_inference_llama:
        provider: "huggingface_inference"
        model_name: "meta-llama/Llama-2-7b-chat-hf"
        temperature: 0.1
        max_tokens: 1000
        api_key_env: "HF_API_TOKEN"  # Required for Llama models
        request_timeout: 120
        max_retries: 3
        streaming: false

      hf_inference_falcon:
        provider: "huggingface_inference"
        model_name: "tiiuae/falcon-7b-instruct"
        temperature: 0.1
        max_tokens: 1000
        api_key_env: "HF_API_TOKEN"  # Optional
        request_timeout: 120
        max_retries: 3
        streaming: false
  prompts:
    templates:
      zero_shot: "prompts/templates/verifier_zero_shot.txt"



# Logging configurations
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levellevel)s - %(message)s"
  log_dir: "data/logs"
