#!/usr/bin/env python3
"""
FactFence - Main Entry Point

This script runs the complete fact verification pipeline:
1. Extract facts from input claims
2. Retrieve relevant knowledge from Knowledge Graph
3. Verify facts against retrieved knowledge
4. Generate verification report
"""

import os
import sys
from datetime import datetime
from pipeline.pipeline import FactFencePipeline
from utils.logger import initialize_logger
from utils.io import load_input_data, save_output_data
from config.config_service import ConfigurationService


def main():
    config_service = ConfigurationService()
    config = config_service.setup()
    logger = initialize_logger(config)

    logger.info("Initializing pipeline")
    pipeline = FactFencePipeline(config)

    # Load input data
    logger.info(f"Loading input data from: {config.input.claims_file}")
    input_data = load_input_data(config.input.claims_file)

    if not input_data:
        logger.error("No input data loaded")
        return 1

    # Run pipeline
    [verification] = pipeline.verify_claims([input_data])

    # Save output data
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(
        config.output.directory, f"verification_results_{timestamp}.json"
    )
    logger.info(f"Saving output data to: {results_file}")
    save_output_data(verification, results_file)

    return 0


if __name__ == "__main__":
    sys.exit(main())
