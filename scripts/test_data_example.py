#!/usr/bin/env python3
"""
Test Data Example Script

This script demonstrates how to use the test data loader to load and process
test data from the test.yaml file.
"""

import os
import sys
from pathlib import Path
from utils.test_data_loader import load_test_data, get_test_item_by_id

# Add the parent directory to sys.path to import project modules
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def main():
    """Main function to demonstrate test data loading and processing."""
    # Path to the test data file
    test_data_path = os.path.join(project_root, "data", "test-data", "test.yaml")

    # Load the test data
    print(f"Loading test data from {test_data_path}...")
    test_data = load_test_data(test_data_path)
    print(f"Loaded {len(test_data.items)} test items")
    print()

    # Print a summary of all test items
    print("Summary of test items:")
    for item in test_data.items:
        print(f"ID: {item.id}")
        print(
            f"Question: {item.question[:50]}..."
            if len(item.question) > 50
            else f"Question: {item.question}"
        )
        print(
            f"Number of triples: {len(item.repo_chat_answer.triples_by_human_expert)}"
        )
        print()

    # Get a specific test item by ID
    test_id = "test_001"
    print(f"Getting test item with ID {test_id}...")
    test_item = get_test_item_by_id(test_data, test_id)

    if test_item:
        print(f"Found test item with ID {test_id}")
        print(f"Question: {test_item.question}")
        print(f"Repo chat answer (excerpt): {test_item.repo_chat_answer.text[:100]}...")
        print(
            f"Human expert answer (excerpt): {test_item.human_expert_answer.text[:100]}..."
        )
        print()

        # Print all triples for this test item
        print(f"Triples for test item {test_id}:")
        for i, triple in enumerate(test_item.repo_chat_answer.triples_by_human_expert):
            print(
                f"Triple {i + 1}: {triple.subject} - {triple.predicate} - {triple.object}"
            )
            if triple.description:
                print(f"  Description: {triple.description}")
        print()
    else:
        print(f"No test item found with ID {test_id}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
