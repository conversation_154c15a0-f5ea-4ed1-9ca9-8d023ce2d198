#!/usr/bin/env python3
"""
Data Import Script for FactFence

This script imports data into the Neo4j knowledge graph from files in the configured import directory.
Supported formats:
- CSV files: *_nodes.csv for nodes and *_rels.csv for relationships
- JSON files: *_nodes.json for nodes and *_rels.json for relationships
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
from kg.neo4j_service import Neo4jService
from kg.data_importer import DataImporter

# Add the parent directory to sys.path to import project modules
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
os.chdir(project_root)


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Set up logging configuration."""
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    return logging.getLogger("data_importer")


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, "r") as f:
        return yaml.safe_load(f)


def main():
    parser = argparse.ArgumentParser(
        description="Import data into FactFence knowledge graph."
    )
    parser.add_argument(
        "--config",
        "-c",
        default="config/settings.yaml",
        help="Path to configuration file",
    )
    parser.add_argument(
        "--import-dir", "-d", help="Directory containing data files to import"
    )
    parser.add_argument(
        "--log-level",
        "-l",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level",
    )
    parser.add_argument(
        "--delete-before-import",
        "-D",
        action="store_true",
        help="Delete all existing data before importing",
    )

    args = parser.parse_args()
    logger = setup_logging(args.log_level)

    try:
        # Load configuration
        logger.info(f"Loading configuration from {args.config}")
        config = load_config(args.config)

        # Initialize Neo4j service
        logger.info("Initializing Neo4j service")
        neo4j_service = Neo4jService(config["knowledge_graph"])

        # Set import directory
        import_config = config["knowledge_graph"]["import"]
        import_dir = args.import_dir or import_config.get("directory")

        # Initialize data importer
        logger.info(f"Initializing data importer with directory: {import_dir}")

        # Override delete_before_import from command line if specified
        if args.delete_before_import:
            import_config["delete_before_import"] = True
            logger.info(
                "Will delete all existing data before import (from command line argument)"
            )

        importer = DataImporter(
            neo4j_service, {"directory": import_dir, **import_config}
        )

        # Import all data
        logger.info("Starting data import")
        success = importer.import_all()

        if success:
            logger.info("Data import completed successfully")
            return 0
        else:
            logger.error("Data import failed")
            return 1

    except Exception as e:
        logger.error(f"Error during data import: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
