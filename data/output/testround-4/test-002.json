{"original_claim": ["Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch."], "extraction": {"original_claim": ["Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch."], "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\n['Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.']\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON>, führen durch, Stichprobenerhebungen)\n(<PERSON><PERSON><PERSON>rden, führen durch, Vergleichsmessungen)", "facts": [{"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}]}, "retrieval": {"original_claim": ["Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch."], "retrievals": [{"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.0}}]}, {"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}}]}]}, "verification": {"original_claim": ["Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch."], "status": "SUPPORTS", "explanation": "Die bereitgestellten Tripel zeigen explizit, dass die Behörden BAG und ENSI Stichprobenerhebungen und Vergleichsmessungen durchführen (\"(Behörden BAG und ENSI, führen aus, Stichprobenerhebung und Vergleichsmessungen)\"). Da diese Behörden zu den Behörden zählen, wird die Aussage durch die vorliegenden Informationen gestützt."}}