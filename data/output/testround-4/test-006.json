{"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "extraction": {"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\n['Das Instandsetzungskonz<PERSON> schre<PERSON> vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden.']\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(Instandsetzungskonzept, sch<PERSON><PERSON><PERSON> vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden)\n(Instandsetzungskonzept, hat Bezeichnung, Das Instandsetzungskonzept)\n(Augmented-Reality-Brillen, werden verwendet für, Dokumentation sämtlicher Massnahmen)", "facts": [{"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden"}, {"subject": "Instandsetzungskonzept", "predicate": "hat Bezeichnung", "object": "Das Instandsetzungskonzept"}, {"subject": "Augmented-Reality-<PERSON><PERSON><PERSON>", "predicate": "werden verwendet für", "object": "Dokumentation sämtlicher Massnahmen"}]}, "retrieval": {"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "retrievals": [{"fact": {"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden"}, "knowledge": []}, {"fact": {"subject": "Instandsetzungskonzept", "predicate": "hat Bezeichnung", "object": "Das Instandsetzungskonzept"}, "knowledge": []}, {"fact": {"subject": "Augmented-Reality-<PERSON><PERSON><PERSON>", "predicate": "werden verwendet für", "object": "Dokumentation sämtlicher Massnahmen"}, "knowledge": []}]}, "verification": {"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "status": "INSUFFICIENT", "explanation": "No knowledge available. We can't verify something without knowledge. Verification prompt is skipped!"}}