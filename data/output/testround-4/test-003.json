{"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "extraction": {"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\n['Ab<PERSON><PERSON>chertes Uran besitzt einen höheren Uran-235-Anteil als Natururan und wird direkt als Brennstoff verwendet.']\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, <PERSON><PERSON><PERSON><PERSON>, <PERSON>öheren Uran-235-<PERSON><PERSON><PERSON> Nat<PERSON>)\n(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, wird ver<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "facts": [{"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "retrievals": [{"fact": {"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}}]}, {"fact": {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:451", "name": "Brennstab", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153057844048691651", "type": "ist gefüllt mit"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:453", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "similarity": 0.83}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2066", "name": "Oxid-<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.8}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152995171885910034", "type": "wird"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2067", "name": "chemisch aufgelöst", "similarity": 0.0}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1554", "name": "<PERSON>sen Folge", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153060043071948306", "type": "tritt auf"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1555", "name": "ein Brennst<PERSON>", "similarity": 0.61}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}}]}]}, "verification": {"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "status": "REFUTES", "explanation": "Die bereitgestellten Informationen besagen, dass abgereichertes Uran einen \"geringeren Anteil\" hat. Da sich der Begriff \"abgereichertes Uran\" auf <PERSON><PERSON> bezieht, ist implizit der Anteil von Uran-235 gemeint. Die Aussage im Claim, dass abgereichertes Uran einen höheren Uran-235-Anteil als Natururan besitzt, wird dadurch widerlegt. Außerdem gibt es keine Information, dass abgereichertes Uran direkt als Brennstoff verwendet wird."}}