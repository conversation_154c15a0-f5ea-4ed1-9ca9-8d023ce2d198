# Test Data 1
```mermaid
graph TD
  Langzeitsicherheit -->|beruhtAuf| Sicherheitsfunktionen

  Sicherheitsfunktionen --> S1
  Sicherheitsfunktionen --> S2
  Sicherheitsfunktionen --> S3
  Sicherheitsfunktionen --> S4
  Sicherheitsfunktionen --> S5

  S1 -->|istEine| Sicherheitsfunktion
  S1 -->|hatBeschreibung| D1
  D1["Isolation der radioaktiven Abfälle von der Erdoberfläche"]

  S2 -->|istEine| Sicherheitsfunktion
  S2 -->|hatBeschreibung| D2
  D2["Vollständiger Einschluss der Radionuklide für eine gewisse Zeit (v.a. für HAA)"]

  S3 -->|istEine| Sicherheitsfunktion
  S3 -->|hatBeschreibung| D3
  D3["Immobilisierung, Rückhaltung und langsame Freisetzung der Radionuklide"]

  S4 -->|istEine| Sicherheitsfunktion
  S4 -->|hatBeschreibung| D4
  D4["Kompatibilität der Elemente des Mehrfachbarrierensystems und der radioaktiven Abfälle untereinander und mit anderen Materialien"]

  S5 -->|istEine| Sicherheitsfunktion
  S5 -->|hatBeschreibung| D5
  D5["Langzeitstabilität des Mehrfachbarrierensystems bezüglich geologischer und klimatischer Langzeitentwicklungen"]
  
```  

```mermaid
graph TD

  gTL -->|hat| Anforderung
  gTL -->|ist| HAA_Lager
  gTL -->|ist| SMA_Lager
  HAA_Lager -->|istIn| LZS
  SMA_Lager -->|istIn| LZS

  HAA_Lager -->|hat| Anforderung_HAA
  SMA_Lager -->|hat| Anforderung_SMA
  Anforderung_HAA -->|ist| Anforderung
  Anforderung_SMA -->|ist| Anforderung

  R1["Möglichst vertikal mittige Lage der Lagerfelder und der übrigen Bauwerke auf Lagerebene im Wirtgestein"] -->|ist| Anforderung
  R2["Genügend großer Abstand aller Untertagbauwerke zueinander"] -->|ist| Anforderung
  R3["Genügend großer seitlicher Abstand der Schächte zu den Lagerfeldern"] -->|ist| Anforderung
  R4["Verschluss des gTL durch Versiegelung und Verfüllung der Hohlräume"] -->|ist| Anforderung
  R5["Möglichst geringe Schädigung des Wirtgesteins durch den Bau"] -->|ist| Anforderung
  R6["Minimierung der Menge an Bewehrungsstahl bzw. anderer metallischer Strukturen oder gasbildenden Stoffe im Tiefenlager"] -->|ist| Anforderung
  R7["Ausreichende Wärmeleitfähigkeit des Ausbaus der Lagerkammern"] -->|ist| Anforderung
  R8["Ausrichtung der Lagerstollen und Lagerkavernen in Richtung der maximalen horizontalen Hauptspannungen (z. B. JO 170°, NL 170°, ZNO 170°)"] -->|ist| Anforderung
  R9["Mindestabstände zu kartierten Störungen (175 m), bestehenden Tiefbohrungen (50 m), sowie zwischen HAA- und SMA-Lager im Kombilager (200 m)"] -->|ist| Anforderung
  R10["Mindestabstand zwischen BEUU und zentralen Bauwerken (80 m)"] -->|ist| Anforderung
  R11["Sicherheitsabstand zwischen Betriebstunnel und Anfang V1-Versiegelung (mind. 100 m)"] -->|ist| Anforderung
  R12["Mindestabstand zwischen Pilot- und Hauptlager (200 m)"] -->|ist| Anforderung
  R13["Separater Zugangstunnel zum Pilotlager als Abzweiger vom Betriebstunnel"] -->|ist| Anforderung

  H1["Ausführung der HAA-Lagerstollen als Blindstollen"] -->|ist| Anforderung_HAA
  H2["Fallende (schichtparallele) Anordnung der HAA-Lagerstollen mit max. Neigung von 10 %"] -->|ist| Anforderung_HAA
  H3["Mindestachsabstand der Lagerstollen von 40 m"] -->|ist| Anforderung_HAA
  H4["Minimaler Durchmesser der Lagerstollen D = 2,50 m"] -->|ist| Anforderung_HAA
  H5["Begrenzung geologischer Auflockerungszonen (Einsatz Schild-TBM und Tübbingen)"] -->|ist| Anforderung_HAA
  H6["Sicherstellung homogener Bentonitverfüllung, inklusive Verfüllung der Hohlräume"] -->|ist| Anforderung_HAA
  H7["Begrenzung der Hydratationswärme des Injektionsmörtels auf max. Paläotemperatur des Opalinustons"] -->|ist| Anforderung_HAA
  H8["Minimierung organischer Beimischungen im Zement des Ausbaus"] -->|ist| Anforderung_HAA

  S1["Mindestens 20 m Transportpfadlänge der Radionuklide im intakten Opalinuston"] -->|ist| Anforderung_SMA
  S2["Ausführung der SMA-Kavernen als Blindkavernen"] -->|ist| Anforderung_SMA
  S3["Mindestachsabstand der SMA-Kavernen von 80 bis 100 m (Planungsannahme: 85 m)"] -->|ist| Anforderung_SMA
  S4["Begrenzung der Länge der horizontalen Lagerkavernen gemäß Schichtneigung des Wirtgesteins"] -->|ist| Anforderung_SMA
  S5["Minimierung der Auflockerungszone durch optimiertes Kavernenprofil und geeignetes Bauverfahren"] -->|ist| Anforderung_SMA
  S6["Sicherstellung, dass keine nachteiligen Zementhydratationswärme-Effekte auftreten"] -->|ist| Anforderung_SMA
  S7["Begrenzung der schädlichen Wechselwirkung zwischen zementgebundenen Einbauten und natürlichen sowie technischen Barrieren"] -->|ist| Anforderung_SMA
  S8["Vermeidung der Mobilisierung von Rad"] -->|ist| Anforderung_SMA
```

# Test Data 2
```mermaid
graph TD

  SMA_Hauptlager -->|hatNormalprofil| Profil_Hauptlager
  SMA_Übernahmebereich -->|hatNormalprofil| Profil_Übernahme
  SMA_Betriebstunnel -->|hatNormalprofil| Profil_Betriebstunnel

  Profil_Hauptlager --> P1["Außendurchmesser (Daussen)"]
  P1 -->|hatWert| 13.00m

  Profil_Hauptlager --> P2["Außenradius (Raussen)"]
  P2 -->|hatWert| 6.50m

  Profil_Hauptlager --> P3["Innendurchmesser (Dinnen)"]
  P3 -->|hatWert| 12.40m

  Profil_Hauptlager --> P4["Innenradius R1 (R1innen)"]
  P4 -->|hatWert| 6.10m

  Profil_Hauptlager --> P5["Innenradius R2 (R2innen)"]
  P5 -->|hatWert| 10.30m

  Profil_Hauptlager --> P6["Innenradius R3 (R3innen)"]
  P6 -->|hatWert| 2.90m

  Profil_Hauptlager --> P7["Innenradius R4 (R4innen)"]
  P7 -->|hatWert| 8.40m

  %% Übernahmebereich parameters
  Profil_Übernahme --> P8["Außendurchmesser (Daussen)"]
  P8 -->|hatWert| 11.00m

  Profil_Übernahme --> P9["Innenradius R1 (R1innen)"]
  P9 -->|hatWert| 9.40m

  Profil_Übernahme --> P10["Innenradius R2 (R2innen)"]
  P10 -->|hatWert| 4.90m

  Profil_Übernahme --> P11["Innenradius R3 (R3innen)"]
  P11 -->|hatWert| 3.30m

  Profil_Betriebstunnel --> P12["Außendurchmesser (Daussen)"]
  P12 -->|hatWert| 6.90m

  Profil_Betriebstunnel --> P13["Außenradius (Raussen)"]
  P13 -->|hatWert| 3.45m

  Profil_Betriebstunnel --> P14["Innendurchmesser (Dinnen)"]
  P14 -->|hatWert| 6.80m

  Profil_Betriebstunnel --> P15["Innenradius (Rinnen)"]
  P15 -->|hatWert| 3.40m
```
# Test Data 3
```mermaid
graph TD

  Trennflächenabstände -->|beziehtSichAuf| Trennflächen
  Trennflächen -->|bilden| Trennflächensystem
  Trennflächenabstände -->|beziehtSichAuf| Kluftflächen
  Trennflächen -->|sind| T1["Natürliche Schwächezonen im Gestein"]
  Trennflächen -->|beeinflussen| MechanischeEigenschaften
  Trennflächen -->|beeinflussen| Gebirgsverhalten
  Gebirgsverhalten -->|relevantFür| BauUntertage
  Gebirgsverhalten -->|relevantFür| StabilitätUntertage


  Trennflächenabstände -->|werdenGemessenDurch| Bohrkern
  Trennflächenorientierungen -->|werdenGemessenDurch| Bohrkern
  Bohrkern -->|entstehenAus| Tiebohrung
  Bohrung_STA2_1 -->|ist| Tiefbohrung
  Bohrung_STA3_1 -->|ist| Tiefbohrung
  Bohrung_BUL1_1 -->|ist| Bohrung_BUL1_1
  Trennflächenabstände -->|zwischen| AufeinanderfolgendeTrennflächen
  AufeinanderfolgendeTrennflächen -->|gehörenZu| SelbesTrennflächensystem
  Trennflächensystem -->|wirdUnterteiltIn| Hauptsystem
  Hauptsystem -->|unterscheidetSichVon| Orientierung
  Hauptsystem -->|unterscheidetSichVon| Häufigkeit
  Hauptsystem -->|umfasst| K1
  Hauptsystem -->|umfasst| K2
  Hauptsystem -->|umfasst| K3
  Trennflächenabstände -->|werdenKorrigiert| ScheinbareAbstände
  ScheinbareAbstände -->|basierendAuf| OrientierungTrennflächen
  ScheinbareAbstände -->|korrigiertZu| TatsächlicheAbstände
  
  Trennflächenabstände -->|werdenAnalysiertDurch| StatistischeMethoden
  StatistischeMethoden -->|ermitteln| Erwartungswert
  StatistischeMethoden -->|ermitteln| Bandbreite
  StatistischeMethoden -->|berücksichtigen| Heterogenität

  Nagra -->|wendetAn| Konzept_Trennflächenabstände
  Konzept_Trennflächenabstände -->|bestehtAus| Messung
  Konzept_Trennflächenabstände -->|bestehtAus| Korrektur_von_ScheinbareAbstände
  Konzept_Trennflächenabstände -->|bestehtAus| StatistischeAnalyse
  Konzept_Trennflächenabstände -->|nutztFür| Standortbewertung
  Standortbewertung -->|umfasst| GeomechanischeEigenschaften
  GeomechanischeEigenschaften -->|ermitteltDurch| Trennflächenanalyse
  Trennflächenkörper -->|ist| Volumina_und_Grundformen_der_durch_Trennflächen_begrenzten_Gesteinskörper
  Trennflächenkörper -->|wirdBerechnetAus| Trennflächenabstände
  Trennflächenkörper -->|wirdBerechnetAus| Trennflächenorientierungen
  Trennflächenkörper -->|istWichtigFür| Beurteilung_der_Stabilität_von_Hohlräumen_im_Gebirge

  Trennflächenanalyse -->|identifiziert| Risiken
  Niederbruch --> |sindErkennbarDurch| Trennflächenabstände
  Niederbruch --> |sindErkennbarDurch| Trennflächenkörper
  UnzulässigeVerformung --> |sindErkennbarDurch| Trennflächenabstände
  UnzulässigeVerformung --> |sindErkennbarDurch| Trennflächenkörper
  Risiken -->|umfassen| Niederbruch
  Risiken -->|umfassen| UnzulässigeVerformung
  Trennflächenanalyse -->|führtZu| PlanungBauverfahren
  PlanungBauverfahren -->|beinhaltet| Sicherungsmaßnahmen
  Sicherungsmaßnahmen -->|gewährleisten| StabilitätBauUndBetrieb

  Nagra -->|trifft| KonservativeAnnahmen
  KonservativeAnnahmen -->|ausgelöstDurch| BegrenzteDatenverfügbarkeit
  KonservativeAnnahmen -->|ausgelöstDurch| HoheVariabilität
  KonservativeAnnahmen -->|ziel| Sicherheitsmaximierung
```
# Test Data 4
```mermaid
graph TD

  %% Core process
  gTL -->|ist| geologischesTiefenlager
  gTL -->|hat| Risikoanalyse
  Risikoanalyse -->|ermittelt| Risiko
  Risikoanalyse -->|bewertet| Gefährdung
  Risikoanalyse -->|basiertAuf| Rahmenbedingungen
  Risikoanalyse -->|basiertAuf| Projekterfahrungen

  %% Bewertung der Gefährdung
  Gefährdung -->|hatMerkmal| Eintretenswahrscheinlichkeit
  Gefährdung -->|hatMerkmal| Auswirkungen

  %% Wahrscheinlichkeitsklassen
  Eintretenswahrscheinlichkeit -->|klassifiziertDurch| DAUB_ITA_Klassifikation
  Eintretenswahrscheinlichkeit -->|hatKlasse| Klasse1
  Eintretenswahrscheinlichkeit -->|hatKlasse| Klasse2
  Eintretenswahrscheinlichkeit -->|hatKlasse| Klasse3
  Eintretenswahrscheinlichkeit -->|hatKlasse| Klasse4

  Klasse1["sehr klein: theoretisch denkbar, praktisch ausgeschlossen"]
  Klasse2["klein: einmaliges Eintreten möglich, vereinzelt dokumentiert"]
  Klasse3["mittel: mehrfaches Eintreten, mehrfach dokumentiert"]
  Klasse4["hoch: häufig zu erwarten, viele dokumentierte Fälle"]

  %% Auswirkungen auf Projektanforderungen
  Projektanforderung -->|hat| Auswirkungen
  Auswirkungen -->|auf| Arbeitssicherheit_und_Gesundheitsschutz
  Auswirkungen -->|auf| Termineinhaltung
  Auswirkungen -->|auf| Kosteneinhaltung
  Auswirkungen -->|auf| Qualität
  Qualität -->|bestehtAus| Einhaltung_Anforderungen_Langzeitsicherheit
  Auswirkungen -->|istUnterteiltIn| 4_Stufen

  Arbeitssicherheit_und_Gesundheitsschutz -->|Skala| AS1["geringfügige Personenschäden ohne mehrtägigen Ausfall"]
  Arbeitssicherheit_und_Gesundheitsschutz -->|Skala| AS4["schwere bleibende Schäden oder Todesfälle"]

  Termineinhaltung -->|Skala| TE1["Verzögerung wenige Tage"]
  Termineinhaltung -->|Skala| TE4[">6 Monate, IBN gefährdet"]

  Kosteneinhaltung -->|Skala| KO1["< 0.1 Mio CHF"]
  Kosteneinhaltung -->|Skala| KO4["> 10 Mio CHF"]

  Qualität -->|Skala| QU1["geringfügige Beeinträchtigungen"]
  Qualität -->|Skala| QU4["schwere Nachteile für LZS"]

  %% Risikowert und Maßnahmen
  Risikoanalyse -->|berechnet| InitialerRisikowert
  InitialerRisikowert -->|basiertAuf| Eintretenswahrscheinlichkeit
  InitialerRisikowert -->|basiertAuf| Auswirkungen
  InitialerRisikowert -->|dientZur| Priorisierung
  InitialerRisikowert -->|dientZur| Maßnahmenplanung

  Maßnahmenplanung -->|beziehtSichAuf| Arbeitssicherheit
  Maßnahmenplanung -->|beziehtSichAuf| Termineinhaltung
  Maßnahmenplanung -->|beziehtSichAuf| Kosteneinhaltung
  Maßnahmenplanung -->|beziehtSichAuf| Qualität
  Maßnahmenplanung -->|unterstützt| LZS
  LZS -->|ist| Langzeitsicherheit
```

# Test Data 5
```mermaid
graph TD

  Risikoanalyse -->|identifiziert| Risiko

  Risiko --> R1["Allgemeine Baugrundrisiken"]
  Risiko --> R2["Spezifische Bauwerksrisiken"]
  Risiko --> R3["Risiken bei Wassereinbrüchen"]

  R1 --> Störung["Störungszonen Typ III & IV"]
  Störung -->|trittAufIn| Homogenbereiche
  Störung -->|führtZu| UnerwarteteBaugrundbedingungen

  R1 --> Baugrund["Baugrundeigenschaften"]
  Baugrund -->|hatUrsache| UnerwarteteDruckhaftigkeit
  Baugrund -->|hatUrsache| Nachbrüchigkeit
  Baugrund -->|hatUrsache| Permeabilität
  Baugrund -->|hatUrsache| AbweichendePorenwasserdruck

  R2 --> Verformung["Unzulässige Verformungen & Versagen des Ausbaus"]
  Verformung -->|hatUrsache| Gebirgsdruck
  Verformung -->|verursacht| VerengungQuerschnitt
  Verformung -->|verursacht| BeschädigungBauwerke

  R2 --> Niederbruch["Niederbruch"]
  Niederbruch -->|hatUrsache| SpannungsinduzierteAblösung
  Niederbruch -->|hatUrsache| AblösungVonFragmentenAusOrtsbrust
  Niederbruch -->|gefährdet| Personal
  Niederbruch -->|gefährdet| Geräte
  Niederbruch -->|gefährdet| Vortriebseinrichtungen

  R2 --> Schild["Verklemmen des Schildes"]
  Schild -->|hatUrsache| RascheVerengung
  Schild -->|begünstigtDurch| DruckhaftesGebirge
  Schild -->|Folge| StillstandVortrieb

  R2 --> Bohrkopf["Blockierter Bohrkopf"]
  Bohrkopf -->|hatUrsache| InstabilitätBohrbereich
  Bohrkopf -->|behindert| Bohrvorgang

  R2 --> Felspfeiler["Versagen des Felspfeilers & angrenzender Ausbau"]
  Felspfeiler -->|hatUrsache| Spannungsumlagerung
  Felspfeiler -->|Folge| Tragfähigkeitsverlust

  %% Wassereinbruchrisiken
  R3 --> WasserKurz["Kurzfristiger Wassereinbruch"]
  WasserKurz -->|hatUrsache| PlötzlicherEintritt_Porenwasser
  WasserKurz -->|Folge| Bauverzögerung
  WasserKurz -->|gefährdet| Sicherheit

  R3 --> WasserLang["Langfristiger Wassereinbruch"]
  WasserLang -->|hatUrsache| HoheDurchlässigkeitGebirge
  WasserLang -->|Folge| DauerhafteGefährdung
```

# Test Data 6
```mermaid
graph TD

  EUU["Erdwissenschaftliche Untersuchungen Untertag (EUU)"]
  EUU -->|Ziel| GeologischeErkundung_Lagerebene
  EUU -->|Ziel| Demonstration_Sicherheitsrelevanter_Technologien
  
  GeologischeErkundung_Lagerebene -->|dientDazu| Bestätigung_Wirtgestein_Eigenschaften
  GeologischeErkundung_Lagerebene -->|dientDazu| Untermauerung_Sicherheitsnachweis

  Demonstration_Sicherheitsrelevanter_Technologien -->|umfasst| Einlagerungstechnologien
  Demonstration_Sicherheitsrelevanter_Technologien -->|umfasst| Rückholungstechnologien
  BEUU["Bauten für EUU"]
  Einlagerungstechnologien -->|werdenGetestetIn| BEUU
  Rückholungstechnologien -->|werdenGetestetIn| BEUU
  Einlagerungstechnologien -->|habenZiel| Funktionsnachweis
  Rückholungstechnologien -->|habenZiel| Funktionsnachweis

  EUU -->|führtZu| ErhöhteSicherheit
  EUU -->|führtZu| ErhöhteZuverlässigkeit
  EUU -->|führtZu| Vertrauensstärkung_Langzeitsicherheit
  EUU -->|führtZu| DesignAnpassung
  EUU -->|führtZu| Ausführungsanpassung
  EUU -->|trägtBeiZu| Erfüllung_Sicherheitsanforderungen
  EUU -->|trägtBeiZu| Gewährleistung_Langzeitsicherheit

  EUU -->|beschriebenIn| Nagra_2021b
  Nagra_2021b["Nagra (2021b): Konzept EUU"]
```

# Test Data 7
```mermaid
graph TD

  SMA -->|ist| Schwach_und_mittel_radioatkive_Abfölle
  SMA_Einlagerung -->|beginntNach| Nukleare_Betriebsbewilligung_SMA
  Nukleare_Betriebsbewilligung_SMA -->|erteiltIn| Jahr2050
  SMA_Einlagerung -->|umfasst| SMA_Pilotlager_Befüllung
  SMA_Pilotlager_Befüllung -->|umfasst| Repräsentative_Auswahl_an_Abfallgebinde
  SMA_Pilotlager_Befüllung -->|führtZu| Versiegelung_SMA_Pilotlager
  Versiegelung_SMA_Pilotlager -->|führtZu| Langzeitüberwachung_SMA

  SMA_Einlagerung -->|beinhaltet| SMA_Hauptlager_Einlagerung
  SMA_Hauptlager_Einlagerung -->|verwendet| Zugangstunnel
  SMA_Hauptlager_Einlagerung -->|verwendet| Zugangsschächte
  Zugangstunnel -->|führenZu| Lagerebene
  Zugangsschächte -->|führenZu| Lagerebene

  HAA_Infrastrukturbau -->|beginntNach| Nukleare_Baubewilligung_HAA
  Nukleare_Baubewilligung_HAA -->|erteiltIn| Jahr2055
  HAA_Infrastrukturbau -->|umfasst| Erschliessung_HAA_Zugänge
  HAA_Infrastrukturbau -->|umfasst| Errichtung_Zentralbereich
  HAA_Infrastrukturbau -->|umfasst| Bau_HAA_Lagerstollen
  HAA_Infrastrukturbau -->|umfasst| Bau_HAA_Pilotlager

  HAA_Einlagerung -->|beginntNach| Nukleare_Betriebsbewilligung_HAA
  Nukleare_Betriebsbewilligung_HAA -->|erteiltIn| Jahr2060
  HAA_Einlagerung -->|umfasst| HAA_Pilotlager_Befüllung
  HAA_Pilotlager_Befüllung -->|umfasst| Ausgewählte_Abfallgebinde
  HAA_Pilotlager_Befüllung -->|führtZu| Verschluss_HAA_Pilotlager
  Verschluss_HAA_Pilotlager -->|führtZu| Überwachung_HAA_Pilotlager

  HAA_Einlagerung -->|umfasst| HAA_Lagerstollen_Einlagerung
  HAA_Einlagerung -->|erfolgtNach| HAA_Pilotlager_Befüllung
  HAA_Lagerstollen_Einlagerung -->|bautParallel| ZweiProJahr
  HAA_Lagerstollen_Einlagerung -->|befülltIm| Folgejahr

  HAA_Lagerstollen_Einlagerung -->|verwendet| Zugangsschächte
  HAA_Lagerstollen_Einlagerung -->|verwendet| Zugangstunnel

  HAA_Einlagerung -->|benötigt| Baustellenlogistik
  Baustellenlogistik -->|verwendet| Betriebsschacht
  Baustellenlogistik -->|verwendet| Zugangstunnel
```

# Test Data 8
```mermaid
graph TD

  UVPV["Verordnung über die Umweltverträglichkeitsprüfung (UVPV)"] -->|veröffentlicht| Jahr1988
  UVPV -->|verlangt| UVP["Umweltverträglichkeitsprüfung"]
  UVP -->|mussDurchgeführtWerdenBei| Umweltbelastende_Anlagen
  Umweltbelastende_Anlagen -->|hatEigenschaft| ErheblicheUmweltbelastung

  UVP -->|hat| Vorgaben
  Vorgaben -->|müssen| UmgesetztWerden

  UVP -->|beinhaltet| UVB["Umweltverträglichkeitsbericht"]
  UVB -->|wirdErstelltFür| Ausgewählter_Standort
  UVB -->|wirdEingereichtMit| Antrag_Rahmenbewilligung

  Dokumentation -->|beinhaltetNicht| WeiterführendeInformation_UVP
  Dokumentation -->|beinhaltetNicht| Ausgang_UVP
```

# Test Data 9
```mermaid
graph TD

  Kompetente_Gesteinseinheiten -->|hat| Störungszonen
  Störungszonen -->|werdenKlassifiziertAls| Typ1
  Störungszonen -->|werdenKlassifiziertAls| Typ2
  Störungszonen -->|werdenKlassifiziertAls| Typ3
  Störungszonen -->|werdenKlassifiziertAls| Typ4
  Störungszonen -->|unterschiedenSichIn| Gesamtausdehnung
  Störungszonen -->|unterschiedenSichIn| AbständeZwischenTrennflächen

  Typ1 -->|hatAusdehnung| WenigeMeterGebirgsbereichen
  Typ1 -->|hatTrennflächenabstand| Dezimeterbereich
  Typ1 -->|Wahrscheinlichkeit_Unentdeckt| Hoch

  Typ4 -->|hatAusdehnung| MehrereDutzendMeter
  Typ4 -->|hatTrennflächenabstand| Dezimeterbereich
  Typ4 -->|hatKernzone| BrekziöseKernzone
  BrekziöseKernzone -->|Eigenschaft| StarkZerrüttet
  Typ4 -->|Wahrscheinlichkeit_Unentdeckt| Niedrig

  Typ1 -->|wenigerAusgedehntAls| Typ2
  Typ2 -->|wenigerAusgedehntAls| Typ3
  Typ3 -->|wenigerAusgedehntAls| Typ4

  Typ1 -->|höhereWahrscheinlichkeit_UnentdecktAls| Typ2
  Typ2 -->|höhereWahrscheinlichkeit_UnentdecktAls| Typ3
  Typ3 -->|höhereWahrscheinlichkeit_UnentdecktAls| Typ4

  ZunehmenderTypenzahl -->|steigt| Gesamtausdehnung
  ZunehmenderTypenzahl -->|sinken| Entdeckungswahrscheinlichkeit
```
