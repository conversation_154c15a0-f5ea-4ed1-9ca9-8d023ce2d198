# FactFence - Fact Verification Framework

FactFence is a framework for verifying factual claims against a knowledge graph.

## Setup

### Prerequisites

- <PERSON>er and Docker Compose
- Python 3.8+
- Required Python packages (install with `pip install -r requirements.txt`)

### Neo4j Setup

The project uses Neo4j as its knowledge graph database. To set up Neo4j:

1. Make sure <PERSON>er and Docker Compose are installed on your system.

2. Start the Neo4j container:

```bash
docker-compose up -d
```

This will start a Neo4j instance with the following configuration:
- Web interface: http://localhost:7474
- Bolt connection: bolt://localhost:7687
- Username: neo4j
- Password: factfence

3. Access the Neo4j browser at http://localhost:7474 and log in with the credentials above.

### Importing Data

To import data into the Neo4j knowledge graph:

1. Place your data files in the `data/graph/import` directory. The following formats are supported:
   - CSV files: `*_nodes.csv` for nodes and `*_rels.csv` for relationships
   - JSON files: `*_nodes.json` for nodes and `*_rels.json` for relationships

2. Use the data importer to import the data:

```python
from kg.neo4j_service import Neo4jService
from kg.data_importer import DataImporter
import yaml

# Load configuration
with open('config/settings.yaml', 'r') as f:
    config = yaml.safe_load(f)

# Initialize Neo4j service
neo4j_service = Neo4jService(config['knowledge_graph'])

# Initialize data importer
importer = DataImporter(neo4j_service, config['knowledge_graph']['import'])

# Import all data
importer.import_all()
```

## Start
```bash
streamlit run factfence.py
```

## License

[MIT License](LICENSE)
