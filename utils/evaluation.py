from typing import List, Dict, Any
from sklearn.metrics import precision_score, recall_score, f1_score, accuracy_score
from pipeline.types.result import Result
from utils.test_data_loader import load_test_data, TestData


def calculate_metrics(results: List[Result], test_data: TestData) -> Dict[str, float]:
    """
    Calculate evaluation metrics for pipeline results.

    Args:
        results: List of pipeline results
        test_data: Test data with ground truth

    Returns:
        Dictionary with precision, recall, F1-score, and accuracy
    """
    # Extract predictions and ground truth
    y_true = []
    y_pred = []

    for result, test_item in zip(results, test_data.items):
        # Convert verification status to binary (True/False)
        # This depends on how your ground truth is structured
        true_value = test_item.human_expert_answer.text == "TRUE"
        pred_value = result.verification.status == "TRUE"

        y_true.append(true_value)
        y_pred.append(pred_value)

    # Calculate metrics
    metrics = {
        "precision": precision_score(y_true, y_pred),
        "recall": recall_score(y_true, y_pred),
        "f1_score": f1_score(y_true, y_pred),
        "accuracy": accuracy_score(y_true, y_pred)
    }

    return metrics


def run_evaluation(pipeline, test_data_path: str) -> Dict[str, float]:
    """
    Run evaluation on test data.

    Args:
        pipeline: FactFencePipeline instance
        test_data_path: Path to test data file

    Returns:
        Dictionary with evaluation metrics
    """
    # Load test data
    test_data = load_test_data(test_data_path)

    # Extract claims from test data
    claims = [item.question for item in test_data.items]

    # Run pipeline on claims
    results = pipeline.verify_claims(claims)

    # Calculate metrics
    metrics = calculate_metrics(results, test_data)

    return metrics