"""
Test Data Loader - Utility for loading and parsing test data from YAML files

This module provides functions and data classes for loading and parsing test data
from YAML files, specifically for the test.yaml format used in the project.
"""

import os
import yaml
from dataclasses import dataclass, field
from typing import List, Optional


@dataclass
class FactTriple:
    """Represents a fact triple (subject, predicate, object) with optional description."""

    subject: str
    predicate: str
    object: str


@dataclass
class RepoAnswer:
    """Represents an answer from the repository chat."""

    text: str
    triples_by_human_expert: List[FactTriple] = field(default_factory=list)


@dataclass
class HumanExpertAnswer:
    """Represents an answer from a human expert."""

    text: str


@dataclass
class TestItem:
    """Represents a single test item from the test data."""

    id: str
    question: str
    repo_chat_answer: RepoAnswer
    human_expert_answer: HumanExpertAnswer


@dataclass
class TestData:
    """Represents the entire test data set."""

    items: List[TestItem] = field(default_factory=list)


#     test_data_path = os.path.join(project_root, "data", "test-data", "test.yaml")
def load_test_data(file_path: str) -> TestData:
    """
    Load test data from a YAML file.

    Args:
        file_path: Path to the YAML file

    Returns:
        TestData object containing the parsed data
    """
    # Check if file exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Test data file not found: {file_path}")

    # Load YAML file
    with open(file_path, "r", encoding="utf-8") as file:
        yaml_data = yaml.safe_load(file)

    # Parse data into TestData object
    test_data = TestData()

    for item_data in yaml_data:
        # Parse triples
        triples = []
        if (
            "repo_chat_answer" in item_data
            and "triples_by_human_expert" in item_data["repo_chat_answer"]
        ):
            for triple_data in item_data["repo_chat_answer"]["triples_by_human_expert"]:
                triple = FactTriple(
                    subject=triple_data["subject"],
                    predicate=triple_data["predicate"],
                    object=triple_data["object"],
                    description=triple_data.get("description"),
                )
                triples.append(triple)

        # Create RepoAnswer
        repo_answer = RepoAnswer(
            text=item_data.get("repo_chat_answer", {}).get("text", ""),
            triples_by_human_expert=triples,
        )

        # Create HumanExpertAnswer
        human_expert_answer = HumanExpertAnswer(
            text=item_data.get("human_expert_answer", {}).get("text", "")
        )

        # Create TestItem
        test_item = TestItem(
            id=item_data.get("id", ""),
            question=item_data.get("question", ""),
            repo_chat_answer=repo_answer,
            human_expert_answer=human_expert_answer,
        )

        # Add to test data
        test_data.items.append(test_item)

    return test_data


def get_test_item_by_id(test_data: TestData, item_id: str) -> Optional[TestItem]:
    """
    Get a test item by its ID.

    Args:
        test_data: TestData object
        item_id: ID of the test item to find

    Returns:
        TestItem if found, None otherwise
    """
    for item in test_data.items:
        if item.id == item_id:
            return item
    return None


if __name__ == "__main__":
    # Example usage
    test_data_path = os.path.join("data", "test-data", "test.yaml")
    try:
        test_data = load_test_data(test_data_path)
        print(f"Loaded {len(test_data.items)} test items")

        # Print the first test item
        if test_data.items:
            first_item = test_data.items[0]
            print(f"First item ID: {first_item.id}")
            print(f"Question: {first_item.question}")
            print(
                f"Number of triples: {len(first_item.repo_chat_answer.triples_by_human_expert)}"
            )

            # Print the first triple if available
            if first_item.repo_chat_answer.triples_by_human_expert:
                first_triple = first_item.repo_chat_answer.triples_by_human_expert[0]
                print(
                    f"First triple: {first_triple.subject} - {first_triple.predicate} - {first_triple.object}"
                )
                if first_triple.description:
                    print(f"Description: {first_triple.description}")

    except Exception as e:
        print(f"Error loading test data: {str(e)}")
