"""
Text Utilities - Helper functions for text processing

This module provides utility functions for text processing and cleaning.
"""


def clean_text(text: str) -> str:
    """
    Clean up text by removing problematic characters.

    Args:
        text: Input text to clean

    Returns:
        Cleaned text
    """
    if not text:
        return ""

    # Remove escape sequences and normalize quotes
    cleaned = text.replace('\\"', "").replace('"', "")
    cleaned = cleaned.replace("\\", "").replace("\n", " ")
    cleaned = cleaned.replace(".", "")
    cleaned = cleaned.replace("\t", " ")

    # Normalize spaces
    cleaned = " ".join(cleaned.split())

    return cleaned
