"""
Fuzzy Retriever - Retrieves knowledge from the knowledge graph using fuzzy matching.

This module implements a retriever that uses fuzzy string matching to link entities
and relations in the knowledge graph to extracted facts.
"""

import logging
from typing import Optional, Union, Dict, List
from thefuzz import fuzz

from pipeline.types.extractor_types import Extraction, FactTriple
from pipeline.types.retriever_types import (
    RetrievalResult,
    RelationKnowledge,
    EntityKnowledge,
    FactKnowledge,
    Retrieval,
)
from config.config_types import AppConfig
from kg import Neo4jService, KnowledgeGraphFactory

logger = logging.getLogger(__name__)


class FuzzyRetriever:
    """
    FuzzyRetriever retrieves relevant knowledge from the knowledge graph
    based on input facts using fuzzy matching.
    """

    kg_service: Union[Neo4jService]
    similarity_threshold: float
    skip_second_stage: bool
    max_entity_matches: int
    max_relation_matches: int

    def __init__(self, config: AppConfig):
        self.kg_service = KnowledgeGraphFactory.create_service(config.knowledge_graph)
        self.similarity_threshold = config.fuzzy_retrieval.similarity_threshold
        self.skip_second_stage = config.fuzzy_retrieval.skip_second_stage
        self.max_entity_matches = config.fuzzy_retrieval.max_entity_matches
        self.max_relation_matches = config.fuzzy_retrieval.max_relation_matches
        logger.info(
            f"FuzzyRetriever initialized with similarity threshold {self.similarity_threshold}, max entity matches {self.max_entity_matches}, max relation matches {self.max_relation_matches}"
        )

    def retrieve(self, extraction: Extraction) -> Retrieval:
        results = []

        for fact in extraction.facts:
            knowledge_list = self.retrieve_for_fact(fact)
            results.append(RetrievalResult(fact=fact, knowledge=knowledge_list))

        return Retrieval(original_claim=extraction.original_claim, retrievals=results)

    def retrieve_for_fact(self, fact: FactTriple) -> List[FactKnowledge]:
        """
        Retrieve knowledge for a single fact with enhanced relation search.

        1. First try to find direct relations between subject and object entities
        2. If no relations found, search for all relations of the object and subject
        3. Handle cases where subject or object knowledge lists are empty
        """
        logger.info(f"Retrieving knowledge for fact: {fact}")

        subject_knowledge_list = self.retrieve_entity(fact.subject)
        object_knowledge_list = self.retrieve_entity(fact.object)

        fact_knowledge_list = []

        # Step 1: Try to find direct relations between subject and object entities
        if subject_knowledge_list and object_knowledge_list:
            logger.info(f"Found {len(subject_knowledge_list)} subjects and {len(object_knowledge_list)} objects, searching for direct relations")

            for subject_knowledge in subject_knowledge_list:
                for object_knowledge in object_knowledge_list:
                    relation_knowledge = self.retrieve_relation(
                        subject_knowledge.id, object_knowledge.id, fact.predicate
                    )

                    if relation_knowledge:
                        fact_knowledge_list.append(FactKnowledge(
                            subject=subject_knowledge,
                            predicate=relation_knowledge,
                            object=object_knowledge,
                        ))

        # Step 2: If no direct relations found, search for all relations of entities
        if not fact_knowledge_list:
            logger.info("No direct relations found, searching for all entity relations")

            # Search relations for object entities (treating them as both subject and object)
            if object_knowledge_list:
                for object_knowledge in object_knowledge_list:
                    fact_knowledge_list.extend(
                        self.retrieve_all_relations_for_entity(object_knowledge, fact.predicate)
                    )

            # Search relations for subject entities (treating them as both subject and object)
            if subject_knowledge_list:
                for subject_knowledge in subject_knowledge_list:
                    fact_knowledge_list.extend(
                        self.retrieve_all_relations_for_entity(subject_knowledge, fact.predicate)
                    )

        # Step 3: Handle cases where subject or object knowledge lists are empty
        if not subject_knowledge_list and object_knowledge_list:
            logger.info("No subject entities found, processing object entities only")
            for object_knowledge in object_knowledge_list:
                fact_knowledge_list.extend(
                    self.retrieve_all_relations_for_entity(object_knowledge, fact.predicate)
                )

        if not object_knowledge_list and subject_knowledge_list:
            logger.info("No object entities found, processing subject entities only")
            for subject_knowledge in subject_knowledge_list:
                fact_knowledge_list.extend(
                    self.retrieve_all_relations_for_entity(subject_knowledge, fact.predicate)
                )

        # If still no knowledge found, create a placeholder
        if not fact_knowledge_list:
            logger.info("No knowledge found at all, creating placeholder")
            fact_knowledge_list.append(FactKnowledge(
                subject=None,
                predicate=None,
                object=None,
            ))

        logger.info(f"Retrieved {len(fact_knowledge_list)} knowledge entries for fact")
        return fact_knowledge_list

    def retrieve_all_relations_for_entity(self, entity_knowledge: EntityKnowledge, predicate: str) -> List[FactKnowledge]:
        """
        Retrieve all relations for an entity, treating it as both subject and object.
        This method does NOT use fuzzy matching for relation search.

        Args:
            entity_knowledge: The entity to search relations for
            predicate: The original predicate (for logging purposes)

        Returns:
            List of FactKnowledge with all found relations
        """
        fact_knowledge_list = []

        try:
            # Get all relations where this entity is the subject
            subject_relations = self.kg_service.get_relationships_of_entity(entity_knowledge.id)

            if subject_relations:
                # Limit the number of relations to process
                limited_relations = subject_relations[:self.max_relation_matches]
                logger.info(f"Found {len(subject_relations)} relations for entity {entity_knowledge.name} as subject, processing {len(limited_relations)}")

                for relation_type in limited_relations:
                    # For each relation, get the connected entities
                    connected_entities = self.kg_service.get_connected_entities(entity_knowledge.id, relation_type)

                    if connected_entities:
                        # Limit connected entities as well
                        limited_connected = connected_entities[:self.max_entity_matches]

                        for connected_entity in limited_connected:
                            fact_knowledge_list.append(FactKnowledge(
                                subject=entity_knowledge,
                                predicate=RelationKnowledge(type=relation_type, similarity=1.0),  # No fuzzy matching
                                object=EntityKnowledge(
                                    id=connected_entity.id,
                                    name=connected_entity.name,
                                    similarity=1.0  # Direct match from KG
                                )
                            ))

            # Get all relations where this entity is the object
            object_relations = self.kg_service.get_incoming_relationships_of_entity(entity_knowledge.id)

            if object_relations:
                # Limit the number of relations to process
                limited_relations = object_relations[:self.max_relation_matches]
                logger.info(f"Found {len(object_relations)} incoming relations for entity {entity_knowledge.name} as object, processing {len(limited_relations)}")

                for relation_type in limited_relations:
                    # For each relation, get the source entities
                    source_entities = self.kg_service.get_source_entities(entity_knowledge.id, relation_type)

                    if source_entities:
                        # Limit source entities as well
                        limited_sources = source_entities[:self.max_entity_matches]

                        for source_entity in limited_sources:
                            fact_knowledge_list.append(FactKnowledge(
                                subject=EntityKnowledge(
                                    id=source_entity.id,
                                    name=source_entity.name,
                                    similarity=1.0  # Direct match from KG
                                ),
                                predicate=RelationKnowledge(type=relation_type, similarity=1.0),  # No fuzzy matching
                                object=entity_knowledge
                            ))

        except Exception as e:
            logger.error(f"Error retrieving all relations for entity {entity_knowledge.name}: {str(e)}")

        logger.info(f"Retrieved {len(fact_knowledge_list)} relation-based knowledge entries for entity {entity_knowledge.name}")
        return fact_knowledge_list

    def retrieve_entity(self, entity_name: str) -> List[EntityKnowledge]:
        if not entity_name:
            return []

        try:
            retrieved_entities = self.kg_service.get_entities_by_name(entity_name, 10)
            matches = []

            for retrieved_entity in retrieved_entities:
                similarity = (
                    fuzz.ratio(entity_name.lower(), retrieved_entity.name.lower()) / 100.0
                )

                if similarity >= self.similarity_threshold:
                    matches.append((retrieved_entity, similarity))

            # Sort by similarity (highest first) and take the top max_entity_matches
            matches.sort(key=lambda x: x[1], reverse=True)
            top_matches = matches[:self.max_entity_matches]

            entity_knowledge_list = []
            for retrieved_entity, similarity in top_matches:
                entity_knowledge_list.append(EntityKnowledge(
                    id=retrieved_entity.id,
                    name=retrieved_entity.name,
                    similarity=similarity,
                ))

            return entity_knowledge_list

        except Exception as e:
            logger.error(f"Error retrieving entity {entity_name}: {str(e)}", exc_info=True)
            exit(1)

    def retrieve_relation(
        self, subject_id: str, object_id: str, predicate: str
    ) -> Optional[RelationKnowledge]:
        try:
            # Get relationships from the knowledge graph service
            relationships = self.kg_service.get_relationships(subject_id, object_id)

            if not relationships:
                return None

            # Find the best matching relationship based on similarity to the predicate
            best_relation = None
            highest_similarity = 0.0

            for relationship in relationships:

                # Calculate similarity between relation type and predicate
                similarity = fuzz.ratio(predicate.lower(), relationship.lower()) / 100.0

                if (
                    similarity > highest_similarity
                    and similarity >= self.similarity_threshold
                ):
                    highest_similarity = similarity
                    best_relation = RelationKnowledge(
                        type=relationship,
                        similarity=similarity,
                    )

            return best_relation

        except Exception as e:
            logger.error(f"Error retrieving relation: {str(e)}")
            exit(1)
