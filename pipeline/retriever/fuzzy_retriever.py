"""
Fuzzy Retriever - Retrieves knowledge from the knowledge graph using fuzzy matching.

This module implements a retriever that uses fuzzy string matching to link entities
and relations in the knowledge graph to extracted facts.
"""

import logging
from typing import Union, List
from thefuzz import fuzz

from pipeline.types.extractor_types import Extraction, FactTriple
from pipeline.types.retriever_types import (
    RetrievalResult,
    RelationKnowledge,
    EntityKnowledge,
    FactKnowledge,
    Retrieval,
)
from config.config_types import AppConfig
from kg import Neo4jService, KnowledgeGraphFactory

logger = logging.getLogger(__name__)


class FuzzyRetriever:
    """
    FuzzyRetriever retrieves relevant knowledge from the knowledge graph
    based on input facts using fuzzy matching.
    """

    kg_service: Union[Neo4jService]
    similarity_threshold: float
    skip_second_stage: bool
    max_entity_matches: int
    max_relation_matches: int

    def __init__(self, config: AppConfig):
        self.kg_service = KnowledgeGraphFactory.create_service(config.knowledge_graph)
        self.similarity_threshold = config.fuzzy_retrieval.similarity_threshold
        self.skip_second_stage = config.fuzzy_retrieval.skip_second_stage
        self.max_entity_matches = config.fuzzy_retrieval.max_entity_matches
        self.max_relation_matches = config.fuzzy_retrieval.max_relation_matches
        logger.info(
            f"FuzzyRetriever initialized with similarity threshold {self.similarity_threshold}, max entity matches {self.max_entity_matches}, max relation matches {self.max_relation_matches}"
        )

    def retrieve(self, extraction: Extraction) -> Retrieval:
        results = []

        for fact in extraction.facts:
            knowledge_list = self.retrieve_for_fact(fact)
            results.append(RetrievalResult(fact=fact, knowledge=knowledge_list))

        return Retrieval(original_claim=extraction.original_claim, retrievals=results)

    def retrieve_for_fact(self, fact: FactTriple) -> List[FactKnowledge]:
        """
        Retrieve knowledge for a single fact with enhanced relation search.

        1. First try to find direct relations between subject and object entities
        2. If no direct relations found, search all relations of the subject entities
        3. Then search all relations of the object entities
        4. Handle cases where subject or object knowledge lists are empty
        """
        logger.info(f"Retrieving knowledge for fact: {fact}")

        subject_knowledge_list = self.retrieve_entity(fact.subject)
        object_knowledge_list = self.retrieve_entity(fact.object)

        fact_knowledge_list = []

        # Step 1: Try to find direct relations between subject and object entities, if both entities are found
        if subject_knowledge_list and object_knowledge_list:
            logger.info(f"Found {len(subject_knowledge_list)} subjects and {len(object_knowledge_list)} objects, searching for direct relations")

            no_relations_found_for_subject = True

            for subject_knowledge in subject_knowledge_list:
                for object_knowledge in object_knowledge_list:
                    relations_list = self.retrieve_relation(subject_knowledge.id, object_knowledge.id)

                    if relations_list:
                        no_relations_found_for_subject = False
                        for relation_knowledge in relations_list:
                            fact_knowledge_list.append(FactKnowledge(
                                subject=subject_knowledge,
                                predicate=relation_knowledge,
                                object=object_knowledge,
                            ))
                    # Step 1b: No relations found, search all relations of the object
                    else:
                        logger.info(f"No direct relations found between {subject_knowledge.name} and {object_knowledge.name}")
                        fact_knowledge_list.extend(self.search_entity_relations(object_knowledge))

                if no_relations_found_for_subject:
                    fact_knowledge_list.extend(self.search_entity_relations(subject_knowledge))
                no_relations_found_for_subject = True

        # Step 2: Handle cases where subject or object knowledge lists are empty
        # Search for the available entities if one list is empty
        if not subject_knowledge_list and object_knowledge_list:
            logger.info("No subject entities found, processing object entities only")
            for object_knowledge in object_knowledge_list:
                fact_knowledge_list.extend(
                    self.search_entity_relations(object_knowledge)
                )

        if not object_knowledge_list and subject_knowledge_list:
            logger.info("No object entities found, processing subject entities only")
            for subject_knowledge in subject_knowledge_list:
                fact_knowledge_list.extend(
                    self.search_entity_relations(subject_knowledge)
                )

        logger.info(f"Retrieved {len(fact_knowledge_list)} knowledge entries for fact")
        return fact_knowledge_list

    def retrieve_entity(self, entity_name: str) -> List[EntityKnowledge]:
        if not entity_name:
            return []

        try:
            retrieved_entities = self.kg_service.get_entities_by_name(entity_name, 10)
            matches = []

            for retrieved_entity in retrieved_entities:
                similarity = (
                    fuzz.ratio(entity_name.lower(), retrieved_entity.name.lower()) / 100.0
                )

                if similarity >= self.similarity_threshold:
                    matches.append((retrieved_entity, similarity))

            # Sort by similarity (highest first) and take the top max_entity_matches
            matches.sort(key=lambda x: x[1], reverse=True)
            top_matches = matches[:self.max_entity_matches]

            entity_knowledge_list = []
            for retrieved_entity, similarity in top_matches:
                entity_knowledge_list.append(EntityKnowledge(
                    id=retrieved_entity.id,
                    name=retrieved_entity.name,
                    similarity=similarity,
                ))

            return entity_knowledge_list

        except Exception as e:
            logger.error(f"Error retrieving entity {entity_name}: {str(e)}", exc_info=True)
            exit(1)

    def retrieve_relation(
        self, subject_id: str, object_id: str
    ) -> List[RelationKnowledge]:
        """
        Get ALL relationships between two entities without predicate filtering or fuzzy matching.

        Args:
            subject_id: ID of the subject entity
            object_id: ID of the object entity

        Returns:
            List of all relations between the entities
        """
        try:
            # Get all relationships from the knowledge graph service
            relationships = self.kg_service.get_relationships(subject_id, object_id, self.max_relation_matches)

            if not relationships:
                return []

            # Return all relations without filtering
            relation_list = []
            for relationship in relationships:
                relation_list.append(RelationKnowledge(
                    type=relationship.type,
                    id=relationship.id,
                ))

            return relation_list

        except Exception as e:
            logger.error(f"Error retrieving relations: {str(e)}")
            exit(1)

    def search_entity_relations(self, entity_knowledge: EntityKnowledge) -> List[FactKnowledge]:
        """
        Search for all relations of an entity and return FactKnowledge entries.
        This includes both outgoing relations (entity as subject) and incoming relations (entity as object).
        Uses efficient single queries to get both relations and entities.

        Args:
            entity_knowledge: The entity to search relations for

        Returns:
            List of FactKnowledge with all found relations
        """
        fact_knowledge_list = []

        try:
            outgoing_relations_and_entities = self.kg_service.get_outgoing_relations_and_entities(
                entity_knowledge.id, self.max_relation_matches
            )

            if outgoing_relations_and_entities:
                logger.info(f"Found {len(outgoing_relations_and_entities)} outgoing relations for entity {entity_knowledge.name}")

                for incoming_relation_and_entity in outgoing_relations_and_entities:
                    fact_knowledge_list.append(FactKnowledge(
                        subject=entity_knowledge,
                        predicate=RelationKnowledge(
                            type=incoming_relation_and_entity.relation.type,
                            id=incoming_relation_and_entity.relation.id
                        ),
                        object=EntityKnowledge(
                            id=incoming_relation_and_entity.entity.id,
                            name=incoming_relation_and_entity.entity.name,
                            similarity=0.0 # TODO: Calculate similarity. Should we at all?
                        )
                    ))

            # Get incoming relations and entities in a single query (entity as object)
            incoming_relations_and_entities = self.kg_service.get_incoming_relations_and_entities(
                entity_knowledge.id, self.max_relation_matches
            )

            if incoming_relations_and_entities:
                logger.info(f"Found {len(incoming_relations_and_entities)} incoming relations for entity {entity_knowledge.name}")

                for incoming_relation_and_entity in incoming_relations_and_entities:
                    fact_knowledge_list.append(FactKnowledge(
                        subject=EntityKnowledge(
                            id=incoming_relation_and_entity.entity.id,
                            name=incoming_relation_and_entity.entity.name,
                            similarity=0.0 # TODO: Calculate similarity. Should we at all?
                        ),
                        predicate=RelationKnowledge(
                            type=incoming_relation_and_entity.relation.type,
                            id=incoming_relation_and_entity.relation.id
                        ),
                        object=entity_knowledge
                    ))

        except Exception as e:
            logger.error(f"Error searching relations for entity {entity_knowledge.name}: {str(e)}")

        logger.info(f"Found {len(fact_knowledge_list)} relation-based knowledge entries for entity {entity_knowledge.name}")
        return fact_knowledge_list
