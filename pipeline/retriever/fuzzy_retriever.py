"""
Fuzzy Retriever - Retrieves knowledge from the knowledge graph using fuzzy matching.

This module implements a retriever that uses fuzzy string matching to link entities
and relations in the knowledge graph to extracted facts.
"""

import logging
from typing import Optional, Union, Dict, List
from thefuzz import fuzz

from pipeline.types.extractor_types import Extraction, FactTriple
from pipeline.types.retriever_types import (
    RetrievalR<PERSON>ult,
    RelationKnowledge,
    EntityKnowledge,
    FactKnowledge,
    Retrieval,
)
from config.config_types import AppConfig
from kg import Neo4jService, KnowledgeGraphFactory

logger = logging.getLogger(__name__)


class FuzzyRetriever:
    """
    FuzzyRetriever retrieves relevant knowledge from the knowledge graph
    based on input facts using fuzzy matching.
    """

    kg_service: Union[Neo4jService]
    similarity_threshold: float
    skip_second_stage: bool
    max_entity_matches: int

    def __init__(self, config: AppConfig):
        self.kg_service = KnowledgeGraphFactory.create_service(config.knowledge_graph)
        self.similarity_threshold = config.fuzzy_retrieval.similarity_threshold
        self.skip_second_stage = config.fuzzy_retrieval.skip_second_stage
        self.max_entity_matches = config.fuzzy_retrieval.max_entity_matches
        logger.info(
            f"FuzzyRetriever initialized with similarity threshold {self.similarity_threshold}, max entity matches {self.max_entity_matches}"
        )

    def retrieve(self, extraction: Extraction) -> Retrieval:
        results = []

        for fact in extraction.facts:
            knowledge_list = self.retrieve_for_fact(fact)
            results.append(RetrievalResult(fact=fact, knowledge=knowledge_list))

        return Retrieval(original_claim=extraction.original_claim, retrievals=results)

    def retrieve_for_fact(self, fact: FactTriple) -> List[FactKnowledge]:
        subject_knowledge_list = self.retrieve_entity(fact.subject)
        object_knowledge_list = self.retrieve_entity(fact.object)

        fact_knowledge_list = []

        # Generate all combinations of subject and object knowledge
        for subject_knowledge in subject_knowledge_list:
            for object_knowledge in object_knowledge_list:
                relation_knowledge = self.retrieve_relation(
                    subject_knowledge.id, object_knowledge.id, fact.predicate
                )

                fact_knowledge_list.append(FactKnowledge(
                    subject=subject_knowledge,
                    predicate=relation_knowledge,
                    object=object_knowledge,
                ))

        # TODO: Not sure yet if I really want that...
        # If no combinations found, create entries with available knowledge
        if not fact_knowledge_list:
            if subject_knowledge_list and object_knowledge_list:
                # If we have both but no relations, create combinations anyway
                for subject_knowledge in subject_knowledge_list:
                    for object_knowledge in object_knowledge_list:
                        fact_knowledge_list.append(FactKnowledge(
                            subject=subject_knowledge,
                            predicate=None,
                            object=object_knowledge,
                        ))
            elif subject_knowledge_list:
                # Only subject found
                for subject_knowledge in subject_knowledge_list:
                    fact_knowledge_list.append(FactKnowledge(
                        subject=subject_knowledge,
                        predicate=None,
                        object=None,
                    ))
            elif object_knowledge_list:
                # Only object found
                for object_knowledge in object_knowledge_list:
                    fact_knowledge_list.append(FactKnowledge(
                        subject=None,
                        predicate=None,
                        object=object_knowledge,
                    ))
            else:
                # Nothing found
                fact_knowledge_list.append(FactKnowledge(
                    subject=None,
                    predicate=None,
                    object=None,
                ))

        return fact_knowledge_list

    def retrieve_entity(self, entity_name: str) -> List[EntityKnowledge]:
        if not entity_name:
            return []

        try:
            retrieved_entities = self.kg_service.get_entities_by_name(entity_name, 10)
            matches = []

            for retrieved_entity in retrieved_entities:
                similarity = (
                    fuzz.ratio(entity_name.lower(), retrieved_entity.name.lower()) / 100.0
                )

                if similarity >= self.similarity_threshold:
                    matches.append((retrieved_entity, similarity))

            # Sort by similarity (highest first) and take the top max_entity_matches
            matches.sort(key=lambda x: x[1], reverse=True)
            top_matches = matches[:self.max_entity_matches]

            entity_knowledge_list = []
            for retrieved_entity, similarity in top_matches:
                entity_knowledge_list.append(EntityKnowledge(
                    id=retrieved_entity.id,
                    name=retrieved_entity.name,
                    similarity=similarity,
                ))

            return entity_knowledge_list

        except Exception as e:
            logger.error(f"Error retrieving entity {entity_name}: {str(e)}", exc_info=True)
            exit(1)

    def retrieve_relation(
        self, subject_id: str, object_id: str, predicate: str
    ) -> Optional[RelationKnowledge]:
        try:
            # Get relationships from the knowledge graph service
            relationships = self.kg_service.get_relationships(subject_id, object_id)

            if not relationships:
                return None

            # Find the best matching relationship based on similarity to the predicate
            best_relation = None
            highest_similarity = 0.0

            for relationship in relationships:

                # Calculate similarity between relation type and predicate
                similarity = fuzz.ratio(predicate.lower(), relationship.lower()) / 100.0

                if (
                    similarity > highest_similarity
                    and similarity >= self.similarity_threshold
                ):
                    highest_similarity = similarity
                    best_relation = RelationKnowledge(
                        type=relationship,
                        similarity=similarity,
                    )

            return best_relation

        except Exception as e:
            logger.error(f"Error retrieving relation: {str(e)}")
            exit(1)
