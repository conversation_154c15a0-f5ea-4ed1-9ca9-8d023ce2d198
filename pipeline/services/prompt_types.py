from dataclasses import dataclass
from typing import Dict, List


@dataclass
class ExtractorPromptData:
    """Input data for prompt templates."""
    input_text: str


@dataclass
class EntityFilteringPromptData:
    """Input data for entity filtering prompt templates."""
    claim: str
    entity_connections: Dict[str, List[str]]


@dataclass
class VerifierPromptData:
    """Input data for verifier prompt templates."""
    claim: str
    knowledge: str