"""
Prompt Service - Manages different prompt strategies for LLM interactions using LlamaIndex

This module provides a service for managing different prompt templates and strategies
for interacting with language models, including zero-shot, few-shot, and chain-of-thought,
using LlamaIndex's prompt templates.
"""

import os
import json
import logging
from typing import Dict, List

from llama_index.core.prompts import RichPromptTemplate

from pipeline.services.prompt_types import ExtractorPromptData, VerifierPromptData, EntityFilteringPromptData
from pipeline.types.extractor_types import (
    FewShotExample,
)
from config.config_types import PromptsConfig

logger = logging.getLogger(__name__)


class PromptService:
    """
    Service for managing different prompt templates and strategies using LlamaIndex.

    This class provides functionality to load and format prompts using
    different strategies like zero-shot, few-shot, and chain-of-thought,
    leveraging LlamaIndex's prompt template system.
    """
    prompts_config: PromptsConfig
    few_shot_examples: Dict[str, List[FewShotExample]]
    prompt_templates: Dict[str, RichPromptTemplate]

    def __init__(self, config: PromptsConfig):
        """
        Initialize the prompt service.

        Args:
            config: Configuration dictionary containing prompt settings
        """
        self.prompts_config = config
        self.few_shot_examples = self._load_few_shot_examples()
        self.prompt_templates = self._load_prompt_templates()
        logger.info("Prompt service initialized")

    def _load_few_shot_examples(self) -> Dict[str, List[FewShotExample]]:
        """
        Load few-shot examples from the configured file.

        Returns:
            Dictionary of few-shot examples by task type
        """
        examples_path = self.prompts_config.few_shot_examples
        if not examples_path:
            logger.warning("No few-shot examples file configured")
            return {}

        if not os.path.exists(examples_path):
            logger.error(f"Few-shot examples file not found: {examples_path}")
            exit(1)

        try:
            with open(examples_path, "r", encoding="utf-8") as f:
                examples = json.load(f)
            logger.info(f"Loaded few-shot examples from {examples_path}")
            return examples
        except Exception as e:
            logger.error(f"Error loading few-shot examples: {str(e)}")
            exit(1)

    def _load_prompt_templates(self) -> Dict[str, RichPromptTemplate]:
        """
        Load prompt templates from files and convert to LlamaIndex RichPromptTemplates.

        Returns:
            Dictionary of LlamaIndex RichPromptTemplates by strategy
        """
        templates: Dict[str, RichPromptTemplate] = {}

        for strategy, path in self.prompts_config.templates.items():
            if not path or not os.path.exists(path):
                logger.error(f"Prompt template file not found: {path}")
                exit(1)

            try:
                with open(path, "r", encoding="utf-8") as f:
                    template_text = f.read()

                templates[strategy] = RichPromptTemplate(template_str=template_text)
                logger.info(f"Loaded prompt template for strategy: {strategy}")
            except Exception as e:
                logger.error(f"Error loading prompt template for {strategy}: {str(e)}")

        return templates

    # TODO: We just support zero-shot for verifier
    def get_verifier_prompt(self, strategy: str, input_data: VerifierPromptData) -> str:
        if strategy not in self.prompt_templates:
            logger.error(f"Strategy {strategy} not found")
            exit(1)

        template = self.prompt_templates[strategy]
        if strategy == "few_shot":
            return self._format_extractor_few_shot(template, input_data)

        input_dict = {"claim": input_data.claim, "knowledge": input_data.knowledge}
        logger.info(f"Prompt {template.format(**input_dict)}")
        return template.format(**input_dict)


    def get_extractor_prompt(self, strategy: str, input_data: ExtractorPromptData) -> str:
        """
        Get a formatted prompt using the specified strategy.

        Args:
            strategy: Prompt strategy to use ('zero_shot', 'few_shot', 'cot', etc.)
            input_data: Input data to format the prompt with

        Returns:
            Formatted prompt string
        """
        # Check if we have the requested strategy
        if strategy not in self.prompt_templates:
            logger.error(f"Strategy {strategy} not found")
            exit(1)

        template = self.prompt_templates[strategy]

        # For few-shot, we need to add examples
        if strategy == "few_shot":
            return self._format_extractor_few_shot(template, input_data)

        # For zero-shot and cot, we can use the template directly
        input_dict = {"input_text": input_data.input_text}
        return template.format(**input_dict)

    # TODO: Implement if needed
    def _format_verifier_few_shot(self, template: RichPromptTemplate, input_data: VerifierPromptData) -> str:
        pass

    def get_entity_filtering_prompt(self, input_data: EntityFilteringPromptData) -> str:
        """
        Get a formatted prompt for entity filtering.

        Args:
            input_data: Input data to format the prompt with

        Returns:
            Formatted prompt string
        """
        if "entity_filtering" not in self.prompt_templates:
            logger.error("Entity filtering prompt template not found")
            exit(1)

        template = self.prompt_templates["entity_filtering"]

        # Format the entity connections as a string
        entity_connections_str = ""
        for entity, connections in input_data.entity_connections.items():
            entity_connections_str += f"{entity}: {connections}\n"

        input_dict = {
            "claim": input_data.claim,
            "entity_connections": entity_connections_str
        }

        return template.format(**input_dict)

    def _format_extractor_few_shot(
        self, template: RichPromptTemplate, input_data: ExtractorPromptData
    ) -> str:
        """
        Format a few-shot prompt with examples.

        Args:
            template: Prompt template
            input_data: Input data to format the prompt with

        Returns:
            Formatted prompt with examples
        """
        examples = self.few_shot_examples.get("fact_extraction", [])
        if not examples:
            logger.warning("No few-shot examples found, falling back to zero-shot")
            input_dict = {"input_text": input_data.input_text}
            return template.format(**input_dict)

        # Build examples section
        examples_text = "\nEXAMPLES:\n"
        for i, example in enumerate(examples):
            examples_text += f"\nExample {i + 1}:\n"
            examples_text += f"Input: {example.get('input', '')}\n"

            # Handle different output formats
            output = example.get("output", "")
            if isinstance(output, list):
                examples_text += "Output:\n"
                for item in output:
                    examples_text += f"- {item}\n"
            elif isinstance(output, dict):
                examples_text += "Output:\n"
                for key, value in output.items():
                    examples_text += f"{key}: {value}\n"
            else:
                examples_text += f"Output: {output}\n"

        # Create a dictionary with input_text and examples
        input_dict = {"input_text": input_data.input_text, "examples": examples_text}

        # Format the template with the input data and examples
        template_text = template.template
        if "{examples}" not in template_text:
            # Insert examples before INPUT section
            parts = template_text.split("INPUT:")
            if len(parts) == 2:
                template_text = parts[0] + "{examples}\n\nYOUR TASK:\nINPUT:" + parts[1]
                template = RichPromptTemplate(template_str=template_text)
            else:
                # If template doesn't have the expected format, append examples at the end
                template_text = template_text + "\n{examples}"
                template = RichPromptTemplate(template_str=template_text)

        return template.format(**input_dict)
