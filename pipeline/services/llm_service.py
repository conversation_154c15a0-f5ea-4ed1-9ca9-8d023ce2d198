"""
LLM Service - Manages interactions with language models using LlamaIndex

This module provides a service for interacting with different language models
through LlamaIndex's LLM interfaces, supporting OpenAI, Anthropic, and HuggingFace models.
"""

import os
import logging
from typing import List, Dict, Any
from llama_index.llms.openai import OpenAI
from llama_index.llms.anthropic import Anthropic
from llama_index.llms.huggingface import Hugging<PERSON>aceLLM
from llama_index.llms.huggingface_api import HuggingFaceInferenceAPI
from llama_index.core.llms import LLM
from llama_index.core.llms import ChatMessage
from config.config_types import ModelConfig, ModelsConfig

logger = logging.getLogger(__name__)


class LLMService:
    """
    Service for interacting with language models using LlamaIndex.

    This class provides a unified interface for interacting with different
    language model providers, including OpenAI and Anthropic, using LlamaIndex's
    LLM interfaces.
    """

    models_config: ModelsConfig
    llm: LLM

    def __init__(self, config: ModelsConfig):
        self.models_config = config
        self.llm = self._initialize_llm()

        logger.info(
            f"LLM service initialized with selected model: {self.models_config.selected}"
        )

    def _initialize_llm(self) -> LLM:
        if self.models_config.selected not in self.models_config.available:
            logger.error(f"Selected model {self.models_config.selected} not available")
            exit(1)

        model_config = self.models_config.available.get(self.models_config.selected)

        try:
            if model_config.provider == "openai":
                return self._init_openai(model_config)
            elif model_config.provider == "anthropic":
                return self._init_anthropic(model_config)
            elif model_config.provider == "huggingface":
                return self._init_huggingface(model_config)
            elif model_config.provider == "huggingface_inference":
                return self._init_huggingface_inference(model_config)
            else:
                logger.error(f"Unsupported provider: {model_config.provider}")
                exit(1)
        except Exception as e:
            logger.error(f"Error initializing LLM: {str(e)}")
            exit(1)

    def _init_openai(self, model_config: ModelConfig) -> LLM:
        api_key = os.environ.get(model_config.api_key_env)
        if not api_key:
            logger.error(
                f"OpenAI API key not found in environment variable: {model_config.api_key_env}"
            )
            exit(1)

        return OpenAI(
            model=model_config.model_name,
            temperature=model_config.temperature,
            max_tokens=model_config.max_tokens,
            api_key=api_key,
            max_retries=model_config.max_retries,
            timeout=model_config.request_timeout,
        )

    def _init_anthropic(self, model_config: ModelConfig) -> LLM:
        api_key = os.environ.get(model_config.api_key_env)
        if not api_key:
            logger.error(
                f"Anthropic API key not found in environment variable: {model_config.api_key_env}"
            )
            exit(1)

        return Anthropic(
            model=model_config.model_name,
            temperature=model_config.temperature,
            max_tokens=model_config.max_tokens,
            api_key=api_key,
            timeout=model_config.request_timeout,
            max_retries=model_config.max_retries,
        )

    def _init_huggingface(self, model_config: ModelConfig) -> LLM:
        api_token = None
        if model_config.api_key_env:
            api_token = os.environ.get(model_config.api_key_env)
            if not api_token and model_config.api_key_env != "None":
                logger.warning(
                    f"HuggingFace API token not found in environment variable: {model_config.api_key_env}. "
                    f"Proceeding with public model access."
                )

        context_window = 4096
        if hasattr(model_config, "context_window"):
            context_window = model_config.context_window

        return HuggingFaceLLM(
            model_name=model_config.model_name,
            temperature=model_config.temperature,
            max_tokens=model_config.max_tokens,
            tokenizer_name=model_config.model_name,  # Usually the same as model_name
            token=api_token,
            context_window=context_window,
            max_retries=model_config.max_retries,
            timeout=model_config.request_timeout,
        )

    def _init_huggingface_inference(self, model_config: ModelConfig) -> LLM:
        api_token = None
        if model_config.api_key_env:
            api_token = os.environ.get(model_config.api_key_env)
            if not api_token:
                logger.warning(
                    f"HuggingFace API token not found in environment variable: {model_config.api_key_env}. "
                    f"Some models may not be accessible without authentication."
                )

        return HuggingFaceInferenceAPI(
            model_name=model_config.model_name,
            temperature=model_config.temperature,
            max_tokens=model_config.max_tokens,
            token=api_token,
            max_retries=model_config.max_retries,
            timeout=model_config.request_timeout,
        )

    def generate(self, prompt: str) -> str:
        """
        Generate text using the configured language model.

        Args:
            prompt: Input prompt

        Returns:
            Generated text
        """
        if not self.llm:
            logger.error("LLM not initialized", exc_info=True)
            exit(1)

        try:
            messages = [ChatMessage(role="user", content=prompt)]
            response = self.llm.chat(messages)
            return response.message.content
        except Exception as e:
            logger.error(f"Error generating text: {str(e)}")
            exit(1)

    def get_available_models(self) -> List[str]:
        """
        Get a list of available models.

        Returns:
            List of model names
        """
        return list(self.models_config.available.keys())
