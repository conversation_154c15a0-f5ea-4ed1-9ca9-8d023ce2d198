"""
Fact Extractor - Extract atomic facts from input claims using LlamaIndex

This module uses LLM-based prompt engineering to extract
atomic facts from input claims for verification, leveraging
LlamaIndex for LLM interactions and prompt management.
"""

import logging
from typing import List

from pipeline.services.llm_service import LLMService
from pipeline.services.prompt_service import PromptService
from pipeline.services.prompt_types import ExtractorPromptData
from pipeline.types.extractor_types import Extraction, FactTriple
from config.config_types import AppConfig
from utils.text_utils import clean_text

logger = logging.getLogger(__name__)


class FactExtractor:
    """
    Extract atomic facts from input claims using LlamaIndex.

    This class processes input claims and extracts atomic facts
    that can be individually verified, using LlamaIndex for
    LLM interactions and prompt management.
    """

    prompt_service: PromptService
    llm_service: LLMService
    extraction_strategy: str

    def __init__(self, config: AppConfig):
        """
        Initialize the fact extractor.

        Args:
            config: Application configuration object
        """
        self.prompt_service = PromptService(config.fact_extraction.prompts)
        self.llm_service = LLMService(config.fact_extraction.models)
        self.extraction_strategy = config.fact_extraction.strategy

        logger.info(
            f"Fact extractor initialized with strategy: {self.extraction_strategy}"
        )

    def extract_facts(self, claim: str) -> Extraction:
        """
        Extract atomic facts from a claim.

        Args:
            claim: Claim

        Returns:
            Extraction object containing facts and extraction prompts
        """
        extraction_prompt = self.prompt_service.get_extractor_prompt(
            self.extraction_strategy, ExtractorPromptData(claim)
        )
        extraction_response = self.llm_service.generate(extraction_prompt)
        facts = self._parse_llm_response(extraction_response)

        return Extraction(
            original_claim=claim,
            facts=facts,
            extraction_prompt_request=extraction_prompt,
            extraction_prompt_result=extraction_response
        )

    # TODO: Implement fallback/retry mechanism
    def _parse_llm_response(self, response: str) -> List[FactTriple]:
        """
        Parse the LLM response to extract facts as triples.

        Args:
            response: LLM response text

        Returns:
            List of extracted facts as FactTriple objects
        """
        facts = []
        lines = [line.strip() for line in response.split("\n") if line.strip()]

        for line in lines:
            # Match the triple format (object, subject, predicate)
            if line.startswith("(") and line.endswith(")"):
                try:
                    # Remove parentheses and split by commas
                    parts = line[1:-1].split(",")
                    if len(parts) == 3:
                        facts.append(
                            FactTriple(
                                subject=clean_text(parts[0].strip()),
                                predicate=clean_text(parts[1].strip()),
                                object=clean_text(parts[2].strip()),
                            )
                        )
                except Exception as e:
                    logger.error(f"Error parsing line: {line}. Error: {str(e)}")

        return facts