import logging
from typing import List

from config.config_types import AppConfig
from pipeline.extractor.extractor import FactExtractor
from pipeline.retriever.fuzzy_retriever import FuzzyRetriever
from pipeline.types.result import Result
from pipeline.verifier.verifier import Verifier

logger = logging.getLogger(__name__)

class FactFencePipeline:
    config: AppConfig
    extractor: FactExtractor
    retriever: FuzzyRetriever
    verifier: Verifier

    def __init__(self, config: AppConfig):
        self.config = config
        self.extractor = FactExtractor(config)
        self.retriever = FuzzyRetriever(config)
        self.verifier = Verifier(config)
        logger.info("FactFence pipeline initialized")

    def verify_claims(self, claims: List[str]) -> List[Result]:
        results: List[Result] = []

        for claim in claims:
            extraction_result = self.extractor.extract_facts(claim)
            retrieval_results = self.retriever.retrieve(extraction_result)
            verification_results = self.verifier.verify(retrieval_results)
            results.append(Result(
                original_claim=claim,
                extraction=extraction_result,
                retrieval=retrieval_results,
                verification=verification_results
            ))

        return results
