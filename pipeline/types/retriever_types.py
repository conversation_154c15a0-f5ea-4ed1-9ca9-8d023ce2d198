"""
Type definitions for the retriever module.

This module contains all type definitions related to knowledge retrieval
to ensure type safety and clear interfaces.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from pipeline.types.extractor_types import FactTriple


@dataclass
class EntityKnowledge:
    """Knowledge about an entity retrieved from the knowledge graph."""

    id: str
    name: str
    similarity: float = 1.0


@dataclass
class RelationKnowledge:
    """Knowledge about a relation retrieved from the knowledge graph."""

    type: str
    similarity: float = 1.0


@dataclass
class FactKnowledge:
    """Knowledge retrieved for a single fact."""

    subject: Optional[EntityKnowledge] = None
    predicate: Optional[RelationKnowledge] = None
    object: Optional[EntityKnowledge] = None

    def is_none(self):
        return self.subject is None and self.predicate is None and self.object is None


@dataclass
class RetrievalResult:
    fact: FactTriple
    knowledge: List[FactKnowledge]

    def calculate_confidence(self) -> float:
        """Calculate overall confidence score for the retrieval."""
        if not self.knowledge:
            return 0.0

        total_confidence = 0.0
        for fact_knowledge in self.knowledge:
            scores = []

            if fact_knowledge.subject:
                scores.append(fact_knowledge.subject.similarity)

            if fact_knowledge.predicate:
                scores.append(fact_knowledge.predicate.similarity)

            if fact_knowledge.object:
                scores.append(fact_knowledge.object.similarity)

            if scores:
                total_confidence += sum(scores) / len(scores)

        return total_confidence / len(self.knowledge)


@dataclass
class Retrieval:
    """Result of knowledge retrieval for a fact."""

    original_claim: str
    retrievals: List[RetrievalResult]
