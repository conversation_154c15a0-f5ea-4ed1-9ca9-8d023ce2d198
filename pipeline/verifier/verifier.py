"""
Fact Verifier - Verifies facts using LLM extraction and fuzzy retrieval

This module combines fact extraction and fuzzy retrieval to verify claims
against a knowledge graph, using an approach similar to FactGenius.
"""

import logging
import re
from typing import List
from config.config_types import AppConfig, VerifierConfig
from pipeline.services.llm_service import LLMService
from pipeline.services.prompt_service import PromptService
from pipeline.services.prompt_types import VerifierPromptData
from pipeline.types.retriever_types import Retrieval, RetrievalResult
from pipeline.types.verifier_types import Verification, VerificationStatus

logger = logging.getLogger(__name__)



class Verifier:
    config: VerifierConfig
    llm_service: LLMService
    prompt_service: PromptService

    def __init__(self, config: AppConfig):
        self.config = config.verifier
        self.llm_service = LLMService(config.verifier.models)
        self.prompt_service = PromptService(config.verifier.prompts)
        logger.info("Verifier initialized")

    def verify(self, retrieval: Retrieval) -> Verification:
        logger.info("Verifying fact...")

        knowledge_text = self._format_knowledge(retrieval.retrievals)

        if not knowledge_text:
            logger.info("No knowledge available. We can't verify something without knowledge")
            return Verification(
                original_claim=retrieval.original_claim,
                status='INSUFFICIENT',
                explanation="No knowledge available. We can't verify something without knowledge. Verification prompt is skipped!"
            )


        prompt_data = VerifierPromptData(
            claim=retrieval.original_claim,
            knowledge=knowledge_text
        )

        prompt = self.prompt_service.get_verifier_prompt(self.config.strategy, prompt_data)

        response = self.llm_service.generate(prompt)
        parsed_response = self._parse_verification_response(response, retrieval.original_claim)

        logger.info(f"Verification result: {parsed_response}")

        # Create and return verification result
        return parsed_response

    def _format_knowledge(self, retrievals: List[RetrievalResult]) -> str:
        knowledge_text = ""
        for retrieval in retrievals:
            # Process each knowledge match for this fact
            for fact_knowledge in retrieval.knowledge:
                if fact_knowledge.is_none():
                    continue

                # TODO: Not sure if 'Unknown' could lead to problems...
                subject_str = fact_knowledge.subject.name if fact_knowledge.subject else "Unknown"
                predicate_str = fact_knowledge.predicate.type if fact_knowledge.predicate else "Unknown"
                object_str = fact_knowledge.object.name if fact_knowledge.object else "Unknown"

                knowledge_text += f"({subject_str}, {predicate_str}, {object_str})\n"

        return knowledge_text

    def _parse_verification_response(self, response: str, original_claim: str) -> Verification:
        """
        Parse the verification response to extract the verification status.

        Args:
            response: LLM response string

        Returns:
            Verification status
        """
        # Look for the verification status in the response
        supports_match = re.search(r"Verification:\s*(SUPPORTS)", response, re.IGNORECASE)
        refutes_match = re.search(r"Verification:\s*(REFUTES)", response, re.IGNORECASE)
        insufficient_match = re.search(r"Verification:\s*(INSUFFICIENT)", response, re.IGNORECASE)
        explanation_match = re.search(r"Explanation:\s*(.*)", response, re.IGNORECASE)
        explanation = 'No explanation provided'
        if explanation_match:
            explanation = explanation_match.group(1)

        verification_status: VerificationStatus = 'INSUFFICIENT'

        if supports_match:
            verification_status = 'SUPPORTS'
        elif refutes_match:
            verification_status = 'REFUTES'
        elif insufficient_match:
            verification_status = 'INSUFFICIENT'
        else:
            # If no status is found, default to INSUFFICIENT
            logger.warning(f"Could not parse verification status from response: {response}")

        return Verification(
            original_claim=original_claim,
            status=verification_status,
            explanation=explanation
        )