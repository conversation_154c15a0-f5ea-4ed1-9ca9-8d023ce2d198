# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.idea/
.idea/*

# Virtual environment
.env/
venv/
ENV/
env.bak/
venv.bak/

# Environment variable files
.env

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# VS Code settings
.vscode/

# macOS system files
.DS_Store

# Python egg files
*.egg
*.egg-info/
dist/
build/
eggs/
parts/
sdist/
var/
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover

# Pytest cache
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Profiling data
*.prof

# Local test config
test_config.json
