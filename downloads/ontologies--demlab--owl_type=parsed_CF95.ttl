@base <http://www.demcare.eu/ontologies/demlab.owl> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix : <#> .
@prefix dc: <http://purl.org/dc/elements/1.1/> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix terms: <http://purl.org/dc/terms/> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix vann: <http://purl.org/vocab/vann/> .
@prefix skos: <http://www.w3.org/2004/02/skos/core#> .
@prefix cc: <http://creativecommons.org/ns#> .
@prefix foaf: <http://xmlns.com/foaf/0.1/> .

<http://purl.org/dc/dcam/VocabularyEncodingScheme>
    a owl:Class .

dc:contributor
    a owl:AnnotationProperty .

dc:coverage
    a owl:AnnotationProperty .

dc:creator
    a owl:AnnotationProperty .

dc:format
    a owl:AnnotationProperty .

dc:identifier
    a owl:AnnotationProperty .

dc:language
    a owl:AnnotationProperty .

dc:publisher
    a owl:AnnotationProperty .

dc:relation
    a owl:AnnotationProperty .

dc:rights
    a owl:AnnotationProperty .

dc:source
    a owl:AnnotationProperty .

dc:subject
    a owl:AnnotationProperty .

dc:type
    a owl:AnnotationProperty .

<http://purl.org/dc/terms/>
    terms:modified "2012-06-14"^^xsd:date ;
    terms:publisher <http://purl.org/dc/aboutdcmi#DCMI> ;
    terms:title "DCMI Metadata Terms - other"@en .

terms:Agent
    terms:description "Examples of Agent include person, organization, and software agent."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Agent-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a terms:AgentClass, owl:Class, owl:NamedIndividual ;
    rdfs:comment "A resource that acts or has the power to act."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Agent"@en .

terms:AgentClass
    terms:description "Examples of Agent Class include groups seen as classes, such as students, women, charities, lecturers."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#AgentClass-003> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2012-06-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A group of agents."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Agent Class"@en ;
    rdfs:subClassOf rdfs:Class .

terms:BibliographicResource
    terms:hasVersion <http://dublincore.org/usage/terms/history/#BibliographicResource-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A book, article, or other documentary resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Bibliographic Resource"@en .

terms:Box
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Box-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of regions in space defined by their geographic coordinates according to the DCMI Box Encoding Scheme."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "DCMI Box"@en ;
    rdfs:seeAlso <http://dublincore.org/documents/dcmi-box/> .

terms:DCMIType
    terms:hasVersion <http://dublincore.org/usage/terms/history/#DCMIType-005> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2012-06-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of classes specified by the DCMI Type Vocabulary, used to categorize the nature or genre of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "DCMI Type Vocabulary"@en ;
    rdfs:seeAlso <http://purl.org/dc/dcmitype/> .

terms:DDC
    terms:hasVersion <http://dublincore.org/usage/terms/history/#DDC-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of conceptual resources specified by the Dewey Decimal Classification."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "DDC"@en ;
    rdfs:seeAlso <http://www.oclc.org/dewey/> .

terms:FileFormat
    terms:description "Examples include the formats defined by the list of Internet Media Types."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#FileFormat-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A digital resource format."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "File Format"@en ;
    rdfs:subClassOf terms:MediaType .

terms:Frequency
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Frequency-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A rate at which something recurs."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Frequency"@en .

terms:IMT
    terms:hasVersion <http://dublincore.org/usage/terms/history/#IMT-004> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of media types specified by the Internet Assigned Numbers Authority."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "IMT"@en ;
    rdfs:seeAlso <http://www.iana.org/assignments/media-types/> .

terms:ISO3166
    terms:hasVersion <http://dublincore.org/usage/terms/history/#ISO3166-004> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of codes listed in ISO 3166-1 for the representation of names of countries."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "ISO 3166"@en ;
    rdfs:seeAlso <http://www.iso.org/iso/en/prods-services/iso3166ma/02iso-3166-code-lists/list-en1.html> .

terms:ISO639-2
    terms:hasVersion <http://dublincore.org/usage/terms/history/#ISO639-2-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The three-letter alphabetic codes listed in ISO639-2 for the representation of names of languages."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "ISO 639-2"@en ;
    rdfs:seeAlso <http://lcweb.loc.gov/standards/iso639-2/langhome.html> .

terms:ISO639-3
    terms:hasVersion <http://dublincore.org/usage/terms/history/#ISO639-3-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of three-letter codes listed in ISO 639-3 for the representation of names of languages."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "ISO 639-3"@en ;
    rdfs:seeAlso <http://www.sil.org/iso639-3/> .

terms:Jurisdiction
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Jurisdiction-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "The extent or range of judicial, law enforcement, or other authority."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Jurisdiction"@en ;
    rdfs:subClassOf terms:LocationPeriodOrJurisdiction .

terms:LCC
    terms:hasVersion <http://dublincore.org/usage/terms/history/#LCC-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of conceptual resources specified by the Library of Congress Classification."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "LCC"@en ;
    rdfs:seeAlso <http://lcweb.loc.gov/catdir/cpso/lcco/lcco.html> .

terms:LCSH
    terms:hasVersion <http://dublincore.org/usage/terms/history/#LCSH-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of labeled concepts specified by the Library of Congress Subject Headings."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "LCSH"@en .

terms:LicenseDocument
    terms:hasVersion <http://dublincore.org/usage/terms/history/#LicenseDocument-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A legal document giving official permission to do something with a Resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "License Document"@en ;
    rdfs:subClassOf terms:RightsStatement .

terms:LinguisticSystem
    terms:description "Examples include written, spoken, sign, and computer languages."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#LinguisticSystem-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A system of signs, symbols, sounds, gestures, or rules used in communication."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Linguistic System"@en .

terms:Location
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Location-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A spatial region or named place."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Location"@en ;
    rdfs:subClassOf terms:LocationPeriodOrJurisdiction .

terms:LocationPeriodOrJurisdiction
    terms:hasVersion <http://dublincore.org/usage/terms/history/#LocationPeriodOrJurisdiction-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A location, period of time, or jurisdiction."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Location, Period, or Jurisdiction"@en .

terms:MESH
    terms:hasVersion <http://dublincore.org/usage/terms/history/#MESH-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of labeled concepts specified by the Medical Subject Headings."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "MeSH"@en ;
    rdfs:seeAlso <http://www.nlm.nih.gov/mesh/meshhome.html> .

terms:MediaType
    terms:hasVersion <http://dublincore.org/usage/terms/history/#MediaType-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A file format or physical medium."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Media Type"@en ;
    rdfs:subClassOf terms:MediaTypeOrExtent .

terms:MediaTypeOrExtent
    terms:hasVersion <http://dublincore.org/usage/terms/history/#MediaTypeOrExtent-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A media type or extent."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Media Type or Extent"@en .

terms:MethodOfAccrual
    terms:hasVersion <http://dublincore.org/usage/terms/history/#MethodOfAccrual-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A method by which resources are added to a collection."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Method of Accrual"@en .

terms:MethodOfInstruction
    terms:hasVersion <http://dublincore.org/usage/terms/history/#MethodOfInstruction-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A process that is used to engender knowledge, attitudes, and skills."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Method of Instruction"@en .

terms:NLM
    terms:hasVersion <http://dublincore.org/usage/terms/history/#NLM-002> ;
    terms:issued "2005-06-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of conceptual resources specified by the National Library of Medicine Classification."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "NLM"@en ;
    rdfs:seeAlso <http://wwwcf.nlm.nih.gov/class/> .

terms:Period
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Period-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of time intervals defined by their limits according to the DCMI Period Encoding Scheme."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "DCMI Period"@en ;
    rdfs:seeAlso <http://dublincore.org/documents/dcmi-period/> .

terms:PeriodOfTime
    terms:hasVersion <http://dublincore.org/usage/terms/history/#PeriodOfTime-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "An interval of time that is named or defined by its start and end dates."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Period of Time"@en ;
    rdfs:subClassOf terms:LocationPeriodOrJurisdiction .

terms:PhysicalMedium
    terms:description "Examples include paper, canvas, or DVD."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#PhysicalMedium-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A physical material or carrier."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Physical Medium"@en ;
    rdfs:subClassOf terms:MediaType .

terms:PhysicalResource
    terms:hasVersion <http://dublincore.org/usage/terms/history/#PhysicalResource-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A material thing."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Physical Resource"@en .

terms:Point
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Point-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of points in space defined by their geographic coordinates according to the DCMI Point Encoding Scheme."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "DCMI Point"@en ;
    rdfs:seeAlso <http://dublincore.org/documents/dcmi-point/> .

terms:Policy
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Policy-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A plan or course of action by an authority, intended to influence and determine decisions, actions, and other matters."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Policy"@en .

terms:ProvenanceStatement
    terms:hasVersion <http://dublincore.org/usage/terms/history/#ProvenanceStatement-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A statement of any changes in ownership and custody of a resource since its creation that are significant for its authenticity, integrity, and interpretation."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Provenance Statement"@en .

terms:RFC1766
    terms:hasVersion <http://dublincore.org/usage/terms/history/#RFC1766-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of tags, constructed according to RFC 1766, for the identification of languages."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "RFC 1766"@en ;
    rdfs:seeAlso <http://www.ietf.org/rfc/rfc1766.txt> .

terms:RFC3066
    terms:description "RFC 3066 has been obsoleted by RFC 4646."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#RFC3066-002> ;
    terms:issued "2002-07-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of tags constructed according to RFC 3066 for the identification of languages."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "RFC 3066"@en ;
    rdfs:seeAlso <http://www.ietf.org/rfc/rfc3066.txt> .

terms:RFC4646
    terms:description "RFC 4646 obsoletes RFC 3066."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#RFC4646-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of tags constructed according to RFC 4646 for the identification of languages."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "RFC 4646"@en ;
    rdfs:seeAlso <http://www.ietf.org/rfc/rfc4646.txt> .

terms:RFC5646
    terms:description "RFC 5646 obsoletes RFC 4646."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#RFC5646-001> ;
    terms:issued "2010-10-11"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of tags constructed according to RFC 5646 for the identification of languages."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "RFC 5646"@en ;
    rdfs:seeAlso <http://www.ietf.org/rfc/rfc5646.txt> .

terms:RightsStatement
    terms:hasVersion <http://dublincore.org/usage/terms/history/#RightsStatement-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A statement about the intellectual property rights (IPR) held in or over a Resource, a legal document giving official permission to do something with a resource, or a statement about access rights."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Rights Statement"@en .

terms:SizeOrDuration
    terms:description "Examples include a number of pages, a specification of length, width, and breadth, or a period in hours, minutes, and seconds."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#SizeOrDuration-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A dimension or extent, or a time taken to play or execute."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Size or Duration"@en ;
    rdfs:subClassOf terms:MediaTypeOrExtent .

terms:Standard
    terms:hasVersion <http://dublincore.org/usage/terms/history/#Standard-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    a owl:Class ;
    rdfs:comment "A basis for comparison; a reference point against which other things can be evaluated."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Standard"@en .

terms:TGN
    terms:hasVersion <http://dublincore.org/usage/terms/history/#TGN-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of places specified by the Getty Thesaurus of Geographic Names."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "TGN"@en ;
    rdfs:seeAlso <http://www.getty.edu/research/tools/vocabulary/tgn/index.html> .

terms:UDC
    terms:hasVersion <http://dublincore.org/usage/terms/history/#UDC-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a <http://purl.org/dc/dcam/VocabularyEncodingScheme>, owl:NamedIndividual ;
    rdfs:comment "The set of conceptual resources specified by the Universal Decimal Classification."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "UDC"@en ;
    rdfs:seeAlso <http://www.udcc.org/> .

terms:URI
    terms:hasVersion <http://dublincore.org/usage/terms/history/#URI-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of identifiers constructed according to the generic syntax for Uniform Resource Identifiers as specified by the Internet Engineering Task Force."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "URI"@en ;
    rdfs:seeAlso <http://www.ietf.org/rfc/rfc3986.txt> .

terms:W3CDTF
    terms:hasVersion <http://dublincore.org/usage/terms/history/#W3CDTF-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a rdfs:Datatype ;
    rdfs:comment "The set of dates and times constructed according to the W3C Date and Time Formats Specification."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "W3C-DTF"@en ;
    rdfs:seeAlso <http://www.w3.org/TR/NOTE-datetime> .

terms:abstract
    terms:hasVersion <http://dublincore.org/usage/terms/history/#abstract-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A summary of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Abstract"@en ;
    rdfs:subPropertyOf dc:description, terms:description .

terms:accessRights
    terms:description "Access Rights may include information regarding access or restrictions based on privacy, security, or other policies."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#accessRights-002> ;
    terms:issued "2003-02-15"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "Information about who can access the resource or an indication of its security status."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Access Rights"@en ;
    rdfs:range terms:RightsStatement ;
    rdfs:subPropertyOf dc:rights, terms:rights .

terms:accrualMethod
    terms:hasVersion <http://dublincore.org/usage/terms/history/#accrualMethod-003> ;
    terms:issued "2005-06-13"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The method by which items are added to a collection."@en ;
    rdfs:domain <http://purl.org/dc/dcmitype/Collection> ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Accrual Method"@en ;
    rdfs:range terms:MethodOfAccrual .

terms:accrualPeriodicity
    terms:hasVersion <http://dublincore.org/usage/terms/history/#accrualPeriodicity-003> ;
    terms:issued "2005-06-13"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The frequency with which items are added to a collection."@en ;
    rdfs:domain <http://purl.org/dc/dcmitype/Collection> ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Accrual Periodicity"@en ;
    rdfs:range terms:Frequency .

terms:accrualPolicy
    terms:hasVersion <http://dublincore.org/usage/terms/history/#accrualPolicy-003> ;
    terms:issued "2005-06-13"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The policy governing the addition of items to a collection."@en ;
    rdfs:domain <http://purl.org/dc/dcmitype/Collection> ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Accrual Policy"@en ;
    rdfs:range terms:Policy .

terms:alternative
    terms:description "The distinction between titles and alternative titles is application-specific."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#alternative-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "An alternative name for the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Alternative Title"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:title, terms:title .

terms:audience
    terms:hasVersion <http://dublincore.org/usage/terms/history/#audience-003> ;
    terms:issued "2001-05-21"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:ObjectProperty ;
    rdfs:comment "A class of entity for whom the resource is intended or useful."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Audience"@en ;
    rdfs:range terms:AgentClass .

terms:available
    terms:hasVersion <http://dublincore.org/usage/terms/history/#available-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date (often a range) that the resource became or will become available."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Available"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:bibliographicCitation
    terms:description "Recommended practice is to include sufficient bibliographic detail to identify the resource as unambiguously as possible."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#bibliographicCitation-002> ;
    terms:issued "2003-02-15"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "A bibliographic reference for the resource."@en ;
    rdfs:domain terms:BibliographicResource ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Bibliographic Citation"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:identifier, terms:identifier .

terms:conformsTo
    terms:hasVersion <http://dublincore.org/usage/terms/history/#conformsTo-003> ;
    terms:issued "2001-05-21"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "An established standard to which the described resource conforms."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Conforms To"@en ;
    rdfs:range terms:Standard ;
    rdfs:subPropertyOf dc:relation, terms:relation .

terms:contributor
    terms:description "Examples of a Contributor include a person, an organization, or a service."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#contributorT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "An entity responsible for making contributions to the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Contributor"@en ;
    rdfs:range terms:Agent ;
    rdfs:subPropertyOf dc:contributor .

terms:coverage
    terms:description "Spatial topic and spatial applicability may be a named place or a location specified by its geographic coordinates. Temporal topic may be a named period, date, or date range. A jurisdiction may be a named administrative entity or a geographic place to which the resource applies. Recommended best practice is to use a controlled vocabulary such as the Thesaurus of Geographic Names [TGN]. Where appropriate, named places or time periods can be used in preference to numeric identifiers such as sets of coordinates or date ranges."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#coverageT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The spatial or temporal topic of the resource, the spatial applicability of the resource, or the jurisdiction under which the resource is relevant."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Coverage"@en ;
    rdfs:range terms:LocationPeriodOrJurisdiction ;
    rdfs:subPropertyOf dc:coverage .

terms:created
    terms:hasVersion <http://dublincore.org/usage/terms/history/#created-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date of creation of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Created"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:creator
    terms:description "Examples of a Creator include a person, an organization, or a service."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#creatorT-002> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "An entity primarily responsible for making the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Creator"@en ;
    rdfs:range terms:Agent ;
    rdfs:subPropertyOf dc:creator, terms:contributor .

terms:date
    terms:description "Date may be used to express temporal information at any level of granularity.  Recommended best practice is to use an encoding scheme, such as the W3CDTF profile of ISO 8601 [W3CDTF]."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#dateT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "A point or period of time associated with an event in the lifecycle of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date .

terms:dateAccepted
    terms:description "Examples of resources to which a Date Accepted may be relevant are a thesis (accepted by a university department) or an article (accepted by a journal)."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#dateAccepted-002> ;
    terms:issued "2002-07-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date of acceptance of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Accepted"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:dateCopyrighted
    terms:hasVersion <http://dublincore.org/usage/terms/history/#dateCopyrighted-002> ;
    terms:issued "2002-07-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date of copyright."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Copyrighted"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:dateSubmitted
    terms:description "Examples of resources to which a Date Submitted may be relevant are a thesis (submitted to a university department) or an article (submitted to a journal)."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#dateSubmitted-002> ;
    terms:issued "2002-07-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date of submission of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Submitted"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:description
    terms:description "Description may include but is not limited to: an abstract, a table of contents, a graphical representation, or a free-text account of the resource."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#descriptionT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "An account of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Description"@en ;
    rdfs:subPropertyOf dc:description .

terms:educationLevel
    terms:hasVersion <http://dublincore.org/usage/terms/history/#educationLevel-002> ;
    terms:issued "2002-07-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "A class of entity, defined in terms of progression through an educational or training context, for which the described resource is intended."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Audience Education Level"@en ;
    rdfs:range terms:AgentClass ;
    rdfs:subPropertyOf terms:audience .

terms:extent
    terms:hasVersion <http://dublincore.org/usage/terms/history/#extent-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The size or duration of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Extent"@en ;
    rdfs:range terms:SizeOrDuration ;
    rdfs:subPropertyOf dc:format, terms:format .

terms:format
    terms:description "Examples of dimensions include size and duration. Recommended best practice is to use a controlled vocabulary such as the list of Internet Media Types [MIME]."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#formatT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The file format, physical medium, or dimensions of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Format"@en ;
    rdfs:range terms:MediaTypeOrExtent ;
    rdfs:subPropertyOf dc:format .

terms:hasFormat
    terms:hasVersion <http://dublincore.org/usage/terms/history/#hasFormat-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is substantially the same as the pre-existing described resource, but in another format."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Has Format"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:hasPart
    terms:hasVersion <http://dublincore.org/usage/terms/history/#hasPart-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is included either physically or logically in the described resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Has Part"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:hasVersion
    terms:hasVersion <http://dublincore.org/usage/terms/history/#hasVersion-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is a version, edition, or adaptation of the described resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Has Version"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:identifier
    terms:description "Recommended best practice is to identify the resource by means of a string conforming to a formal identification system. "@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#identifierT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "An unambiguous reference to the resource within a given context."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Identifier"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:identifier .

terms:instructionalMethod
    terms:description "Instructional Method will typically include ways of presenting instructional materials or conducting instructional activities, patterns of learner-to-learner and learner-to-instructor interactions, and mechanisms by which group and individual levels of learning are measured.  Instructional methods include all aspects of the instruction and learning processes from planning and implementation through evaluation and feedback."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#instructionalMethod-002> ;
    terms:issued "2005-06-13"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:ObjectProperty ;
    rdfs:comment "A process, used to engender knowledge, attitudes and skills, that the described resource is designed to support."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Instructional Method"@en ;
    rdfs:range terms:MethodOfInstruction .

terms:isFormatOf
    terms:hasVersion <http://dublincore.org/usage/terms/history/#isFormatOf-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is substantially the same as the described resource, but in another format."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Is Format Of"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:isPartOf
    terms:hasVersion <http://dublincore.org/usage/terms/history/#isPartOf-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource in which the described resource is physically or logically included."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Is Part Of"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:isReferencedBy
    terms:hasVersion <http://dublincore.org/usage/terms/history/#isReferencedBy-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that references, cites, or otherwise points to the described resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Is Referenced By"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:isReplacedBy
    terms:hasVersion <http://dublincore.org/usage/terms/history/#isReplacedBy-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that supplants, displaces, or supersedes the described resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Is Replaced By"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:isRequiredBy
    terms:hasVersion <http://dublincore.org/usage/terms/history/#isRequiredBy-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that requires the described resource to support its function, delivery, or coherence."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Is Required By"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:isVersionOf
    terms:description "Changes in version imply substantive changes in content rather than differences in format."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#isVersionOf-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource of which the described resource is a version, edition, or adaptation."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Is Version Of"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:issued
    terms:hasVersion <http://dublincore.org/usage/terms/history/#issued-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date of formal issuance (e.g., publication) of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Issued"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:language
    terms:description "Recommended best practice is to use a controlled vocabulary such as RFC 4646 [RFC4646]."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#languageT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "A language of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Language"@en ;
    rdfs:range terms:LinguisticSystem ;
    rdfs:subPropertyOf dc:language .

terms:license
    terms:hasVersion <http://dublincore.org/usage/terms/history/#license-002> ;
    terms:issued "2004-06-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "A legal document giving official permission to do something with the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "License"@en ;
    rdfs:range terms:LicenseDocument ;
    rdfs:subPropertyOf dc:rights, terms:rights .

terms:mediator
    terms:description "In an educational context, a mediator might be a parent, teacher, teaching assistant, or care-giver."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#mediator-003> ;
    terms:issued "2001-05-21"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "An entity that mediates access to the resource and for whom the resource is intended or useful."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Mediator"@en ;
    rdfs:range terms:AgentClass ;
    rdfs:subPropertyOf terms:audience .

terms:medium
    terms:hasVersion <http://dublincore.org/usage/terms/history/#medium-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The material or physical carrier of the resource."@en ;
    rdfs:domain terms:PhysicalResource ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Medium"@en ;
    rdfs:range terms:PhysicalMedium ;
    rdfs:subPropertyOf dc:format, terms:format .

terms:modified
    terms:hasVersion <http://dublincore.org/usage/terms/history/#modified-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date on which the resource was changed."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Modified"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

terms:provenance
    terms:description "The statement may include a description of any changes successive custodians made to the resource."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#provenance-002> ;
    terms:issued "2004-09-20"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:ObjectProperty ;
    rdfs:comment "A statement of any changes in ownership and custody of the resource since its creation that are significant for its authenticity, integrity, and interpretation."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Provenance"@en ;
    rdfs:range terms:ProvenanceStatement .

terms:publisher
    terms:description "Examples of a Publisher include a person, an organization, or a service."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#publisherT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "An entity responsible for making the resource available."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Publisher"@en ;
    rdfs:range terms:Agent ;
    rdfs:subPropertyOf dc:publisher .

terms:references
    terms:hasVersion <http://dublincore.org/usage/terms/history/#references-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is referenced, cited, or otherwise pointed to by the described resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "References"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:relation
    terms:description "Recommended best practice is to identify the related resource by means of a string conforming to a formal identification system. "@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#relationT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Relation"@en ;
    rdfs:subPropertyOf dc:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:replaces
    terms:hasVersion <http://dublincore.org/usage/terms/history/#replaces-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is supplanted, displaced, or superseded by the described resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Replaces"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:requires
    terms:hasVersion <http://dublincore.org/usage/terms/history/#requires-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource that is required by the described resource to support its function, delivery, or coherence."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Requires"@en ;
    rdfs:subPropertyOf dc:relation, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:rights
    terms:description "Typically, rights information includes a statement about various property rights associated with the resource, including intellectual property rights."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#rightsT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "Information about rights held in and over the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Rights"@en ;
    rdfs:range terms:RightsStatement ;
    rdfs:subPropertyOf dc:rights .

terms:rightsHolder
    terms:hasVersion <http://dublincore.org/usage/terms/history/#rightsHolder-002> ;
    terms:issued "2004-06-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:ObjectProperty ;
    rdfs:comment "A person or organization owning or managing rights over the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Rights Holder"@en ;
    rdfs:range terms:Agent .

terms:source
    terms:description "The described resource may be derived from the related resource in whole or in part. Recommended best practice is to identify the related resource by means of a string conforming to a formal identification system."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#sourceT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A related resource from which the described resource is derived."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Source"@en ;
    rdfs:subPropertyOf dc:source, terms:relation ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:spatial
    terms:hasVersion <http://dublincore.org/usage/terms/history/#spatial-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "Spatial characteristics of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Spatial Coverage"@en ;
    rdfs:range terms:Location ;
    rdfs:subPropertyOf dc:coverage, terms:coverage .

terms:subject
    terms:description "Typically, the subject will be represented using keywords, key phrases, or classification codes. Recommended best practice is to use a controlled vocabulary."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#subjectT-002> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2012-06-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "The topic of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Subject"@en ;
    rdfs:subPropertyOf dc:subject ;
    skos:note "This term is intended to be used with non-literal values as defined in the DCMI Abstract Model (http://dublincore.org/documents/abstract-model/).  As of December 2007, the DCMI Usage Board is seeking a way to express this intention with a formal range declaration."@en .

terms:tableOfContents
    terms:hasVersion <http://dublincore.org/usage/terms/history/#tableOfContents-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty ;
    rdfs:comment "A list of subunits of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Table Of Contents"@en ;
    rdfs:subPropertyOf dc:description, terms:description .

terms:temporal
    terms:hasVersion <http://dublincore.org/usage/terms/history/#temporal-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "Temporal characteristics of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Temporal Coverage"@en ;
    rdfs:range terms:PeriodOfTime ;
    rdfs:subPropertyOf dc:coverage, terms:coverage .

terms:title
    terms:hasVersion <http://dublincore.org/usage/terms/history/#titleT-002> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2010-10-11"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "A name given to the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Title"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:title .

terms:type
    terms:description "Recommended best practice is to use a controlled vocabulary such as the DCMI Type Vocabulary [DCMITYPE]. To describe the file format, physical medium, or dimensions of the resource, use the Format element."@en ;
    terms:hasVersion <http://dublincore.org/usage/terms/history/#typeT-001> ;
    terms:issued "2008-01-14"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:ObjectProperty ;
    rdfs:comment "The nature or genre of the resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Type"@en ;
    rdfs:range rdfs:Class ;
    rdfs:subPropertyOf dc:type .

terms:valid
    terms:hasVersion <http://dublincore.org/usage/terms/history/#valid-003> ;
    terms:issued "2000-07-11"^^xsd:date ;
    terms:modified "2008-01-14"^^xsd:date ;
    a owl:AnnotationProperty, owl:DatatypeProperty ;
    rdfs:comment "Date (often a range) of validity of a resource."@en ;
    rdfs:isDefinedBy <http://purl.org/dc/terms/> ;
    rdfs:label "Date Valid"@en ;
    rdfs:range rdfs:Literal ;
    rdfs:subPropertyOf dc:date, terms:date .

vann:preferredNamespacePrefix
    a owl:AnnotationProperty .

<>
    cc:license <http://creativecommons.org/licenses/by/3.0/> ;
    dc:creator <http://www.iti.gr/iti/people/Georgios_Meditskos.html#gmeditsk>, <http://www.iti.gr/iti/people/Ioannis_Kompatsiaris.html#ikom> ;
    dc:description """The ontology has been developed in the framework of the Dem@Care project for representing the experimentation protocol towards diagnostic support and assessment of Dementia in a controlled environment. The aim of the protocol is to provide a brief overview of their health status of the participants during consultation (cognition, behaviours and function), and to correlate the system (sensor) data with the data collected using typical dementia care assessment tools.

The ontology describes the basic Steps of the experimentation protocol (Directed Activities, Semi-directed Activities and Free Discussion with the Clinician), the Tasks that are involved, as well as the measurement types relevant to each Task. The defined protocol can be reused and further extended, adding new steps, tasks and measured data.

Documentation regarding the ontology vocabulary can be found at http://www.demcare.eu/ontologies/demlab.html"""@en ;
    terms:date "2014-09-02" ;
    terms:rights "This ontology is licensed under Creative Commons CC BY 3.0 It is developed in the framework of the Dem@Care project (http://www.demcare.eu/) and supported by the Information Technologies Institute (http://www.iti.gr) of the Centre for Research and Technology Hellas."@en ;
    terms:title "Dem@Care Lab Ontology for Dementia Assessment"@en ;
    vann:preferredNamespacePrefix "demlab" ;
    a owl:NamedIndividual, owl:Ontology ;
    rdfs:seeAlso "http://www.demcare.eu/", "http://www.demcare.eu/downloads/D8.1ExperimentationProtocols.pdf" ;
    owl:imports <http://www.w3.org/2006/time>, <http://xmlns.com/foaf/0.1/> ;
    owl:versionIRI <demlab.owl/1.0> ;
    owl:versionInfo 1.0 .

:ActivityStatusType
    a owl:Class ;
    owl:equivalentClass [
        a owl:Class ;
        owl:oneOf (:completed
            :incomplete
            :omitted
        )
    ] .

:AnswerPhoneTask
    a owl:Class ;
    rdfs:comment """- The phone is scheduled to ring 10 minutes after the entering of the participant inside the room. 
- During this task, the clinician explains that there is a mistake on the phone bill: the amount was wrong. Consequently, it is expected that the participant corrects his/her phone check and corrects the account balance (tasks normally established before the phone call according to the constraint)."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:ArticulationControlTask
    a owl:Class ;
    rdfs:comment """The participant is asked to pronounce repeatedly a predefined diadochokinetic “Pataka” token as rapid as possible stopping this action upon a sign by the clinician. The clinician will stop the action in 10 sec.

This task is used for assessing:
- Latency: time of latency between the end of instructions (stimulation) and the beginning of the task
- Stress level
- Voice: e.g. speech rate, pause rate etc."""@en ;
    rdfs:subClassOf :DirectedTask, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:LatencyData
                :StressData
                :VoiceData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:AutonomyAssessment
    a owl:Class ;
    rdfs:comment "Autonomy is assessed based on the performance of the participant during the SemiDirectedActivitiesStep. The property iadl_e (Instrumental Activities of Daily Living for Elderly) can be used to quantify autonomy"@en ;
    rdfs:seeAlso :SemiDirectedActivitiesStep, :iadl_e ;
    rdfs:subClassOf :ClinicalAssessment, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :iadl_e
    ] .

:CallPsychologistTask
    a owl:Class ;
    rdfs:comment """- The participant has to call the psychologist. Two phone numbers are proposed to the participant (1 visit card of the hair dressing,+ 1 visit card of the CHUN with phone number of a psychologist). 
- According the constraint, the participant has to call the psychologist within the 5 first minutes."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:ClinicalAssessment
    a owl:Class ;
    rdfs:comment "Root concept for the five types of clinical assessment"@en ;
    rdfs:seeAlso :AutonomyAssessment, :CognitiveAbilitiesAssessment, :DiagnosisAssessment, :MotricityAbilitiesAssessment, :NeuropsychiatricAndMoodAssessment .

:ClinicalRecord
    a owl:Class ;
    rdfs:comment "A ClinicalRecord contains information about the five types of clinical assessment"@en ;
    rdfs:seeAlso :ClinicalAssessment, :containsAssessment ;
    rdfs:subClassOf [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:AutonomyAssessment
                :CognitiveAbilitiesAssessment
                :DiagnosisAssessment
                :MotricityAbilitiesAssessment
                :NeuropsychiatricAndMoodAssessment
            )
        ] ;
        owl:onProperty :containsAssessment
    ], [
        a owl:Restriction ;
        owl:onClass :Participant ;
        owl:onProperty :isClinicalRecordOf ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:CognitiveAbilitiesAssessment
    a owl:Class ;
    rdfs:comment """The Cognitive abilities assessment is performed in terms of five scores:
- Mini-Mental State Exam (MMSE). 
- Frontal Assessment Battery (FAB). 
- Trail making test A and B. 
- Short Cognitive Battery. 
- The Free and Cued Selective Reminding Test"""@en ;
    rdfs:seeAlso :fabScore, :freeAndCuedRemindingAssessment, :hasMMSEScore, :shortCognitiveBatteryAssessment, :trailMakingTest ;
    rdfs:subClassOf :ClinicalAssessment, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :shortCognitiveBatteryAssessment
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :fabScore
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :hasMMSEScore
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :freeAndCuedRemindingAssessment
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :trailMakingTest
    ] .

:CognitiveAbilitiesData
    a owl:Class ;
    rdfs:comment "Explore functional abilities for the completion of specific activities"@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:onDataRange xsd:boolean ;
        owl:onProperty :compliantInstructionsOrder ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :repetitions
    ], [
        a owl:Restriction ;
        owl:onClass :ActivityStatusType ;
        owl:onProperty :activityStatus ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:CountingBackwardsTask
    a owl:Class ;
    rdfs:comment """(MonoTask) The participant is standing and the clinician asks him/her to count aloud backwards: 
- From 305 to 285 (to change tens and hundreds) one by one; or 
- From 20 to 0 if they make counting mistakes after 2 attempts of the previous backwards counting

This task is used for assessing:
- Latency: time of latency between the end of instructions (stimulation) and the beginning of the task
- Stress level
- Voice: e.g. speech fluency and articulation"""@en ;
    rdfs:subClassOf :DirectedTask, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:LatencyData
                :StressData
                :VoiceData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:DiagnosisAssessment
    a owl:Class ;
    rdfs:comment "Diagnosis established the day of the recording session. Participant is assigned to one of this 3 categories: Healthy Control participants, Alzheimer’s disease at pre-dementia stage, Alzheimer ’s disease at dementia stage"@en ;
    rdfs:seeAlso :DiagnosisType, :HealthyControlParticipant, :PersonWithDementia, :PersonWithPredementia, :hasClinicalDiagnosis ;
    rdfs:subClassOf :ClinicalAssessment, [
        a owl:Restriction ;
        owl:onClass :DiagnosisType ;
        owl:onProperty :hasClinicalDiagnosis ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:DiagnosisType
    a owl:Class ;
    owl:equivalentClass [
        a owl:Class ;
        owl:oneOf (:healthy
            :dementia
            :predementia
        )
    ] .

:DirectedActivitiesStep
    a owl:Class ;
    rdfs:comment "This ProtocolStep is conducted by a clinician, who details step by step the different activities to do by the participant. This step involves five Tasks. The tasks Walking, CountingBackwards and WalkingAndCountingBackwards are used for characterizing participants’ gait in mono and dual tasks, and the impact of cognitive activity on gait (e.g., walking speed, step length, stops during the walking exercise done in dual task). The tasks SentenceRepeating and ArticulationControl are vocally-directed tasks: one task consists of repeating a sentence after the clinician (this task is repeated with three sentences), and the other is an articulation control exercise done to evaluate the neuromuscular mechanism of speech production."@en ;
    rdfs:subClassOf :ProtocolStep, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:ArticulationControlTask
                :CountingBackwardsTask
                :SentenceRepeatingTask
                :Task
                :WalkingCountingBackwardsTask
                :WalkingTask
            )
        ] ;
        owl:onProperty :involvesTask
    ] .

:DirectedDiscussionTask
    a owl:Class ;
    rdfs:subClassOf :DiscussionTask .

:DirectedExpressionTask
    a owl:Class ;
    rdfs:comment "The clinician asks the same questions than the ones asked on the read article, e.g. What is the topic of the article?"@en ;
    rdfs:subClassOf :DirectedDiscussionTask .

:DirectedTask
    a owl:Class ;
    rdfs:comment "Root concept for grouping the Tasks relevant to the DirectedActivitiesStep"@en ;
    rdfs:seeAlso :DirectedActivitiesStep, :Task ;
    rdfs:subClassOf :Task .

:DiscussionInvolvementData
    a owl:Class ;
    rdfs:comment """- Assessment of the impact of cognitive load imposed by the picture description task on speech fluency
- Memory and self-appraisal assessment
- Verbal fluency and mood (Apathy) assessment"""@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :verbalReactionSpontaneity
    ] .

:DiscussionTask
    a owl:Class ;
    rdfs:comment """Root concept for grouping the Tasks relevant to the DiscussionWithClinicianStep

The relevant tasks are used for assessing:
- Stress level
- Voice: e.g. speech rate, pause rate etc."""@en ;
    rdfs:subClassOf :Task, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:StressData
                :VoiceData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:DiscussionWithClinicianStep
    a owl:Class ;
    rdfs:comment "This ProtocolStep involves two Tasks. The first task (DirectedExpressionTask) is a directed discussion during which the clinician assesses the episodic memory of participant through questions related to events and activities of SemiDirectedActivitiesStep. The second task (PictureDescriptionTask) assesses the verbal fluency and mood of participant using a picture that the participant has to describe (with the picture in the eyes) and then to involve a discussion about their interests for the activity represented. This part is done with two different pictures."@en ;
    rdfs:subClassOf :ProtocolStep, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:DirectedExpressionTask
                :PictureDescriptionTask
                :PictureDiscussionTask
            )
        ] ;
        owl:onProperty :involvesTask
    ] .

:EstablishAccountBalanceTask
    a owl:Class ;
    rdfs:comment """- Three bills (Electricity/Gaz/Phone) are proposed. The account balance is given. 
- The participant has to establish the amount balance after having taken into account the three bills."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:FindBusLineOnMapTask
    a owl:Class ;
    rdfs:comment """- A real bus map is showed to the participant with the indication of the current location of the participant. 
- The participant has to write on a sheet of paper located next to the bus map the bus lines to take for the itinerary (From Hospital to Station train). Two itineraries are accepted."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:FreeDiscussionTask
    a owl:Class ;
    rdfs:subClassOf :DiscussionTask .

:FunctionalAbilitiesData
    a owl:Class ;
    rdfs:comment "Explore functional abilities for the completion of specific activities"@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:onClass :HandTrajectoryType ;
        owl:onProperty :handTrajectory ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:GaitData
    a owl:Class ;
    rdfs:comment "Gait assessment and impact of a cognitive activity on gait performance"@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :stepLength
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :dynamicBalance
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :walkingSpeedInstantaneous
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :walkingSpeed
    ], [
        a owl:Restriction ;
        owl:onDataRange xsd:boolean ;
        owl:onProperty :stoppingDisplacement ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:HandTrajectoryType
    a owl:Class .

:HealthyControlParticipant
    a owl:Class ;
    rdfs:comment "A Healthy Control Participant is defined as the participant whose clinical record contains a healthy clinical diagnosis"@en ;
    rdfs:seeAlso :ClinicalRecord, :DiagnosisAssessment, :DiagnosisType, :healthy ;
    rdfs:subClassOf :Participant ;
    owl:equivalentClass [
        a owl:Restriction ;
        owl:onProperty :hasClinicalRecord ;
        owl:someValuesFrom [
            a owl:Class ;
            owl:intersectionOf (:ClinicalRecord
                [
                    a owl:Restriction ;
                    owl:onProperty :containsAssessment ;
                    owl:someValuesFrom [
                        a owl:Class ;
                        owl:intersectionOf (:DiagnosisAssessment
                            [
                                a owl:Restriction ;
                                owl:onProperty :hasClinicalDiagnosis ;
                                owl:someValuesFrom [
                                    a owl:Class ;
                                    owl:oneOf (:healthy
                                    )
                                ]
                            ]
                        )
                    ]
                ]
            )
        ]
    ] .

:LatencyData
    a owl:Class ;
    rdfs:comment "Explore the time of latency [stimulation, response] during the DirectedActivitiesTask (Explore sensitivity of this measure for assessing the apathy)"@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :latency
    ] .

:LeaveRoomTask
    a owl:Class ;
    rdfs:comment "The participant has to leave the room when he/she feels that all activities are performed and achieved"@en ;
    rdfs:subClassOf :SemiDirectedTask .

:MeasuredData
    a owl:Class ;
    rdfs:comment "MeasuredData is the root concept that groups the measurement types that are collected during the Tasks." .

:MotricityAbilitiesAssessment
    a owl:Class ;
    rdfs:comment """Motricity abilities assessment:
- Part III of the Unified Parkinson’s Disease Rating Scale (UPDRS) ."""@en ;
    rdfs:seeAlso :hasUPDRSScore ;
    rdfs:subClassOf :ClinicalAssessment, [
        a owl:Restriction ;
        owl:onDataRange xsd:int ;
        owl:onProperty :hasUPDRSScore ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:NeuropsychiatricAndMoodAssessment
    a owl:Class ;
    rdfs:comment """Neuropsychiatric/Mood assessments:
- NPI 
- DSM-IV Criteria for depression 
- Apathy Inventory (AI) and diagnostic criteria for apathy."""@en ;
    rdfs:seeAlso :apathyInventory, :dsm_ivDepressionCriteria, :npi ;
    rdfs:subClassOf :ClinicalAssessment, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :apathyInventory
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :npi
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :dsm_ivDepressionCriteria
    ] .

:OrganisationEfficiencyData
    a owl:Class ;
    rdfs:comment "Explore ability to organize with efficiency the different activities"@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:onProperty :areasOfNoInterestExplored ;
        owl:someValuesFrom :Zone
    ], [
        a owl:Restriction ;
        owl:onProperty :areasOfInterestExplored ;
        owl:someValuesFrom :Zone
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :distanceWalked
    ] .

:Participant
    a owl:Class ;
    rdfs:subClassOf foaf:Person, [
        a owl:Restriction ;
        owl:onProperty :participates ;
        owl:someValuesFrom :Protocol
    ], [
        a owl:Restriction ;
        owl:onClass :ClinicalRecord ;
        owl:onProperty :hasClinicalRecord ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:PayBillTask
    a owl:Class ;
    rdfs:comment """- Three bills (Electricity/Gaz/Phone) are proposed. The participant has to pay the phone bill by check. 
- The check is well completed if the amount, order (i.e. Phone Company), date and signature are right."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:PersonWithDementia
    a owl:Class ;
    rdfs:comment "A Person with Dementia is defined as the participant whose clinical record contains a dementia clinical diagnosis"@en ;
    rdfs:seeAlso :ClinicalRecord, :DiagnosisAssessment, :DiagnosisType, :dementia ;
    rdfs:subClassOf :Participant ;
    owl:equivalentClass [
        a owl:Restriction ;
        owl:onProperty :hasClinicalRecord ;
        owl:someValuesFrom [
            a owl:Class ;
            owl:intersectionOf (:ClinicalRecord
                [
                    a owl:Restriction ;
                    owl:onProperty :containsAssessment ;
                    owl:someValuesFrom [
                        a owl:Class ;
                        owl:intersectionOf (:DiagnosisAssessment
                            [
                                a owl:Restriction ;
                                owl:onProperty :hasClinicalDiagnosis ;
                                owl:someValuesFrom [
                                    a owl:Class ;
                                    owl:oneOf (:dementia
                                    )
                                ]
                            ]
                        )
                    ]
                ]
            )
        ]
    ] .

:PersonWithPredementia
    a owl:Class ;
    rdfs:comment "A Person with Predementia is defined as the participant whose clinical record contains a predementia clinical diagnosis"@en ;
    rdfs:seeAlso :ClinicalRecord, :DiagnosisAssessment, :DiagnosisType, :predementia ;
    rdfs:subClassOf :Participant ;
    owl:equivalentClass [
        a owl:Restriction ;
        owl:onProperty :hasClinicalRecord ;
        owl:someValuesFrom [
            a owl:Class ;
            owl:intersectionOf (:ClinicalRecord
                [
                    a owl:Restriction ;
                    owl:onProperty :containsAssessment ;
                    owl:someValuesFrom [
                        a owl:Class ;
                        owl:intersectionOf (:DiagnosisAssessment
                            [
                                a owl:Restriction ;
                                owl:onProperty :hasClinicalDiagnosis ;
                                owl:someValuesFrom [
                                    a owl:Class ;
                                    owl:oneOf (:predementia
                                    )
                                ]
                            ]
                        )
                    ]
                ]
            )
        ]
    ] .

:PictureDescriptionTask
    a owl:Class ;
    rdfs:comment "The clinician shows to the participant one picture representing daily activity and asks the participants to give a verbal description of the picture in a few sentences"@en ;
    rdfs:subClassOf :FreeDiscussionTask .

:PictureDiscussionTask
    a owl:Class ;
    rdfs:comment "The clinician asks if the participant likes doing this activity and why"@en ;
    rdfs:subClassOf :FreeDiscussionTask .

:PrepareDrugBoxTask
    a owl:Class ;
    rdfs:comment """- 4 types of drugs are proposed to the participant. 
- The participant has to respect the posology prescribed (2 types of drugs at different moment of the day) and the date (day and moment of the day)."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:PrepareHotTeaTask
    a owl:Class ;
    rdfs:comment """- The participant has to switch on the electric kettle. 
- The participant has to pour the hot water on the glass containing the tea bag."""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:Protocol
    a owl:Class ;
    rdfs:comment "A Protocol instance contains information regarding the participant and the duration of the protocol session. It can be also associated with one or more ProtocolSteps that define the Tasks and the corresponding MeasuredData."@en ;
    rdfs:seeAlso :MeasuredData, :ProtocolStep, :Task ;
    rdfs:subClassOf [
        a owl:Restriction ;
        owl:onProperty :hasProtocolStep ;
        owl:someValuesFrom :ProtocolStep
    ], [
        a owl:Restriction ;
        owl:onClass <http://www.w3.org/2006/time#Instant> ;
        owl:onProperty <http://www.w3.org/2006/time#hasEnd> ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ], [
        a owl:Restriction ;
        owl:onClass <http://www.w3.org/2006/time#Instant> ;
        owl:onProperty <http://www.w3.org/2006/time#hasBeginning> ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ], [
        a owl:Restriction ;
        owl:onClass foaf:Person ;
        owl:onProperty :hasParticipant ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:ProtocolStep
    a owl:Class ;
    rdfs:comment "A ProtocolStep defines the Tasks of interest. Three ProtocolStep types are currently defined in Dem@Care: DirectedActivities, SemiDirectedActivities and DiscussionWithTheClinician"@en ;
    rdfs:seeAlso :DirectedActivitiesStep, :DiscussionWithClinicianStep, :SemiDirectedActivitiesStep, :Task ;
    rdfs:subClassOf [
        a owl:Restriction ;
        owl:onProperty :involvesTask ;
        owl:someValuesFrom :Task
    ], [
        a owl:Restriction ;
        owl:onClass <http://www.w3.org/2006/time#Instant> ;
        owl:onProperty <http://www.w3.org/2006/time#hasEnd> ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ], [
        a owl:Restriction ;
        owl:onClass <http://www.w3.org/2006/time#Instant> ;
        owl:onProperty <http://www.w3.org/2006/time#hasBeginning> ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:ReadArticleTask
    a owl:Class ;
    rdfs:comment """Three articles are proposed to the participant (Topics: Collection Cars; Cooking; Sightseeing). The three articles have the same words number and level of difficulty. Below each articles, the 3 following questions are asked
- Q1/ What is the topic of the article? 
- Q2/ What is the percentage of French interested in the … (Collection Car / Cooking / Sightseeing, text adapted to the article topic)? 
- Q3/ Who is interviewed?"""@en ;
    rdfs:subClassOf :SemiDirectedTask .

:SemiDirectedActivitiesStep
    a owl:Class ;
    rdfs:comment "This ProtocolStep consists of assessing the autonomy of the participant. The participant has to organize himself/herself and correctly perform a list of Instrumental Activities of Daily Living (e.g., managing finance, using phone, preparing tea) within a timeframe of 15minutes. For this step, the participant is alone in the experimental setting and can refer to the instruction sheet of paper with the IADLs to perform at any time."@en ;
    rdfs:subClassOf :ProtocolStep, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:AnswerPhoneTask
                :CallPsychologistTask
                :EstablishAccountBalanceTask
                :FindBusLineOnMapTask
                :LeaveRoomTask
                :PayBillTask
                :PrepareDrugBoxTask
                :PrepareHotTeaTask
                :Task
                :TurnOnTVTask
                :WaterPlantTask
            )
        ] ;
        owl:onProperty :involvesTask
    ] .

:SemiDirectedTask
    a owl:Class ;
    rdfs:comment """In this Task the participant has to organize himself/herself and correctly perform a list of Instrumental Activities of Daily Living (e.g., managing finance, using phone, preparing tea) within a timeframe of 15minutes.

The relevant tasks are used for assessing:
- Cognitive abilities through activity repetitions, missed activities, order of activities
- Functional abilities for the completion of specific activities: e.g. hand trajectories
- Stress level"""@en ;
    rdfs:subClassOf :Task, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:CognitiveAbilitiesData
                :FunctionalAbilitiesData
                :StressData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:SentenceRepeatingTask
    a owl:Class ;
    rdfs:comment """- The participant is asked to repeat a short sentence after the assessor. 
- The assessor reads aloud a sentence. The participant repeats the sentence. The action is done once by sentence. Three sentences must be repeated.

This task is used for assessing:
- Latency: time of latency between the end of instructions (stimulation) and the beginning of the task
- Stress level
- Voice: e.g. verbal reaction time"""@en ;
    rdfs:subClassOf :DirectedTask, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:LatencyData
                :StressData
                :VoiceData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:StressData
    a owl:Class ;
    rdfs:comment "Explore stress level"@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :stressLevel
    ] .

:Task
    a owl:Class ;
    rdfs:comment "", "Each Task involves the collection of one or more measurements (MeasuredData instances)."@en ;
    rdfs:subClassOf [
        a owl:Restriction ;
        owl:onClass <http://www.w3.org/2006/time#Instant> ;
        owl:onProperty <http://www.w3.org/2006/time#hasBeginning> ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ], [
        a owl:Restriction ;
        owl:onProperty :measuredData ;
        owl:someValuesFrom :MeasuredData
    ], [
        a owl:Restriction ;
        owl:onClass <http://www.w3.org/2006/time#Instant> ;
        owl:onProperty <http://www.w3.org/2006/time#hasEnd> ;
        owl:qualifiedCardinality "1"^^xsd:nonNegativeInteger
    ] .

:TurnOnTVTask
    a owl:Class ;
    rdfs:comment "The TV is turn off. The participant has to take the remote control and turns on the TV"@en ;
    rdfs:subClassOf :SemiDirectedTask .

:VoiceData
    a owl:Class ;
    rdfs:comment """- Vocal biomarkers extraction for cognitive load assessment
- Assessment of mutual influence of cognitive activities an motor activities
- Assessment of the verbal reaction time and the impact of cognitive load imposed by the sentence recall task on speech fluency
- Assessment of the level of the participants’ control over the neuromuscular mechanism of speech production"""@en ;
    rdfs:subClassOf :MeasuredData, [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :articulation
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :speechFluency
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :vowelDuration
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :verbalReactionTime
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :pauseRate
    ], [
        a owl:Restriction ;
        owl:cardinality "1"^^xsd:nonNegativeInteger ;
        owl:onProperty :speechRate
    ] .

:WalkingCountingBackwardsTask
    a owl:Class ;
    rdfs:comment """(DualTask) The clinician asks the participant to walk and count aloud backwards simultaneously: 
- From 305 until the end of the walking tasks; or 
- From 20 to 0 for participants who don’t manage to count backwards from 305 to 285 during the CountingBackwardsTask

This task is used for assessing:
- Gait: walking speed, step length, etc.
- Latency: time of latency between the end of instructions (stimulation) and the beginning of the task
- Stress level
- Voice: e.g. speech fluency and articulation"""@en ;
    rdfs:subClassOf :DirectedTask, [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:GaitData
                :LatencyData
                :StressData
                :VoiceData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:WalkingTask
    a owl:Class ;
    rdfs:comment """(MonoTask) The clinician asks the participant to walk 4 meters across the room, to turn and then to come back (total walking distance: 8m).

This task is used for assessing:
- Gait: walking speed, step length, etc.
- Latency: time of latency between the end of instructions (stimulation) and the beginning of the task
- Stress level"""@en ;
    rdfs:subClassOf :DirectedTask, [
        a owl:Restriction ;
        owl:onProperty :measuredData ;
        owl:someValuesFrom :LatencyData
    ], [
        a owl:Restriction ;
        owl:onProperty :measuredData ;
        owl:someValuesFrom :GaitData
    ], [
        a owl:Restriction ;
        owl:allValuesFrom [
            a owl:Class ;
            owl:unionOf (:GaitData
                :LatencyData
                :StressData
            )
        ] ;
        owl:onProperty :measuredData
    ] .

:WaterPlantTask
    a owl:Class ;
    rdfs:comment "The participant has to take the water can, and water the plant nearby."@en ;
    rdfs:subClassOf :SemiDirectedTask .

:Zone
    a owl:Class ;
    rdfs:comment "Allows the definition of zones (areas) of interest, e.g. phone zone"@en .

:activityStatus
    a owl:ObjectProperty ;
    rdfs:comment "The status of a semi-directed activity" ;
    rdfs:domain :CognitiveAbilitiesData ;
    rdfs:range :ActivityStatusType ;
    rdfs:seeAlso :ActivityStatusType, :CognitiveAbilitiesData .

:apathyInventory
    a owl:DatatypeProperty ;
    rdfs:comment "Apathy Inventory (AI) and diagnostic criteria for apathy"@en ;
    rdfs:domain :NeuropsychiatricAndMoodAssessment ;
    rdfs:seeAlso :NeuropsychiatricAndMoodAssessment .

:areasOfInterestExplored
    a owl:ObjectProperty ;
    rdfs:comment "Zones of no interest"@en ;
    rdfs:domain :OrganisationEfficiencyData ;
    rdfs:range :Zone ;
    rdfs:seeAlso :OrganisationEfficiencyData .

:areasOfNoInterestExplored
    a owl:ObjectProperty ;
    rdfs:comment "Zones of interest"@en ;
    rdfs:domain :OrganisationEfficiencyData ;
    rdfs:range :Zone ;
    rdfs:seeAlso :OrganisationEfficiencyData .

:articulation
    a owl:DatatypeProperty ;
    rdfs:comment "Measures the articulation control of the participant"@en ;
    rdfs:domain :VoiceData .

:completed
    a :ActivityStatusType, owl:NamedIndividual .

:compliantInstructionsOrder
    a owl:DatatypeProperty ;
    rdfs:comment "Whether the participant followed the activity order suggested by the instructions sheet"@en ;
    rdfs:domain :CognitiveAbilitiesData ;
    rdfs:seeAlso :CognitiveAbilitiesData .

:containsAssessment
    a owl:ObjectProperty ;
    rdfs:comment "A ClinicalRecord contains a ClinicalAssessment" ;
    rdfs:domain :ClinicalRecord ;
    rdfs:range :ClinicalAssessment ;
    rdfs:seeAlso :ClinicalAssessment, :ClinicalRecord .

:dementia
    a :DiagnosisType, owl:NamedIndividual .

:distanceWalked
    a owl:DatatypeProperty ;
    rdfs:comment "Total distance walked"@en ;
    rdfs:domain :OrganisationEfficiencyData ;
    rdfs:seeAlso :OrganisationEfficiencyData .

:dsm_ivDepressionCriteria
    a owl:DatatypeProperty ;
    rdfs:comment "DSM-IV Criteria for depression"@en ;
    rdfs:domain :NeuropsychiatricAndMoodAssessment ;
    rdfs:seeAlso "http://en.wikipedia.org/wiki/Diagnostic_and_Statistical_Manual_of_Mental_Disorders"@en .

:dynamicBalance
    a owl:DatatypeProperty ;
    rdfs:comment "Dynamical balance during the walking (e.g., people trajectory study)"@en ;
    rdfs:domain :GaitData ;
    rdfs:seeAlso :GaitData .

:fabScore
    a owl:DatatypeProperty ;
    rdfs:comment "Frontal Assessment Battery score"@en ;
    rdfs:domain :CognitiveAbilitiesAssessment ;
    rdfs:seeAlso :CognitiveAbilitiesAssessment .

:freeAndCuedRemindingAssessment
    a owl:DatatypeProperty ;
    rdfs:comment "The Free and Cued Selective Reminding Test"@en ;
    rdfs:domain :CognitiveAbilitiesAssessment ;
    rdfs:seeAlso :CognitiveAbilitiesAssessment .

:handTrajectory
    a owl:ObjectProperty ;
    rdfs:comment "Associates an instance of FunctionalAbilitiesData with a hand trajectory type"@en ;
    rdfs:domain :FunctionalAbilitiesData ;
    rdfs:range :HandTrajectoryType ;
    rdfs:seeAlso :FunctionalAbilitiesData, :HandTrajectoryType .

:hasClinicalDiagnosis
    a owl:ObjectProperty ;
    rdfs:comment "The value of the DiagnosisAssessment"@en ;
    rdfs:domain :DiagnosisAssessment ;
    rdfs:range :DiagnosisType ;
    rdfs:seeAlso :DiagnosisAssessment, :DiagnosisType ;
    rdfs:subPropertyOf owl:topObjectProperty .

:hasClinicalRecord
    a owl:ObjectProperty ;
    rdfs:comment "Each participant is associated with a clinical record"@en ;
    rdfs:domain :Participant ;
    rdfs:range :ClinicalRecord ;
    rdfs:seeAlso :ClinicalRecord .

:hasMMSEScore
    a owl:DatatypeProperty ;
    rdfs:comment "Mini-Mental State Exam score"@en ;
    rdfs:domain :CognitiveAbilitiesAssessment ;
    rdfs:seeAlso :CognitiveAbilitiesAssessment .

:hasParticipant
    a owl:ObjectProperty ;
    rdfs:comment "The participant of the protocol"@en ;
    rdfs:domain :Protocol ;
    rdfs:range :Participant ;
    rdfs:seeAlso :Person, :Protocol .

:hasProtocolStep
    a owl:ObjectProperty ;
    rdfs:comment "Each Protocol consists of one or more ProtocolSteps"@en ;
    rdfs:domain :Protocol ;
    rdfs:range :ProtocolStep ;
    rdfs:seeAlso :Protocol, :ProtocolStep .

:hasUPDRSScore
    a owl:DatatypeProperty ;
    rdfs:comment "Part III of the Unified Parkinson’s Disease Rating Scale"@en ;
    rdfs:domain :MotricityAbilitiesAssessment ;
    rdfs:seeAlso :MotricityAbilitiesAssessment .

:healthy
    :hasClinicalDiagnosis :dementia ;
    a :DiagnosisType, owl:NamedIndividual .

:iadl_e
    a owl:DatatypeProperty ;
    rdfs:comment "Allows the quantification of autonomy"@en ;
    rdfs:domain :AutonomyAssessment ;
    rdfs:seeAlso :AutonomyAssessment .

:incomplete
    a :ActivityStatusType, owl:NamedIndividual .

:involvesTask
    a owl:ObjectProperty ;
    rdfs:comment "Each ProtocolStep involves one or more Tasks"@en ;
    rdfs:domain :ProtocolStep ;
    rdfs:range :Task ;
    rdfs:seeAlso :ProtocolStep, :Task .

:isClinicalRecordOf
    a owl:ObjectProperty ;
    owl:inverseOf :hasClinicalRecord .

:latency
    a owl:DatatypeProperty ;
    rdfs:comment "Time of latency between the end of instructions (stimulation) and the beginning of the task"@en ;
    rdfs:domain :LatencyData ;
    rdfs:seeAlso :LatencyData .

:measuredData
    a owl:ObjectProperty ;
    rdfs:domain :Task ;
    rdfs:range :MeasuredData ;
    rdfs:seeAlso :MeasuredData, :Task, "Associates a Task with a MeasuredData instance"@en .

:npi
    a owl:DatatypeProperty ;
    rdfs:comment "Neuropsychiatric Inventory"@en ;
    rdfs:domain :NeuropsychiatricAndMoodAssessment ;
    rdfs:seeAlso :NeuropsychiatricAndMoodAssessment .

:omitted
    a :ActivityStatusType, owl:NamedIndividual .

:participates
    a owl:ObjectProperty ;
    rdfs:seeAlso :hasParticipant ;
    owl:inverseOf :hasParticipant .

:pauseRate
    a owl:DatatypeProperty ;
    rdfs:comment "Pause rate during sentence repetition and mono/dual tasks"@en ;
    rdfs:domain :VoiceData ;
    rdfs:seeAlso :ArticulationControlTask, :CountingBackwardsTask, :SentenceRepeatingTask, :VoiceData, :WalkingCountingBackwardsTask .

:predementia
    a :DiagnosisType, owl:NamedIndividual .

:repetitions
    a owl:DatatypeProperty ;
    rdfs:comment "Number of repetitions during semi-directed activities"@en ;
    rdfs:domain :CognitiveAbilitiesData ;
    rdfs:seeAlso :SemiDirectedTask .

:shortCognitiveBatteryAssessment
    a owl:DatatypeProperty ;
    rdfs:comment "Short Cognitive Battery"@en ;
    rdfs:domain :CognitiveAbilitiesAssessment ;
    rdfs:seeAlso :CognitiveAbilitiesAssessment .

:speechFluency
    a owl:DatatypeProperty ;
    rdfs:comment "Speech fluency during sentence repetition and picture description tasks"@en ;
    rdfs:domain :VoiceData ;
    rdfs:seeAlso :VoiceData .

:speechRate
    a owl:DatatypeProperty ;
    rdfs:comment "Speech fluency during sentence repetition and picture description tasks"@en ;
    rdfs:domain :VoiceData ;
    rdfs:seeAlso :VoiceData .

:stepLength
    a owl:DatatypeProperty ;
    rdfs:comment "Step length"@en ;
    rdfs:domain :GaitData ;
    rdfs:seeAlso :GaitData .

:stoppingDisplacement
    a owl:DatatypeProperty ;
    rdfs:comment "Stopping displacement during walking"@en ;
    rdfs:domain :GaitData ;
    rdfs:seeAlso :GaitData .

:stressLevel
    a owl:DatatypeProperty ;
    rdfs:domain :StressData ;
    rdfs:seeAlso :StressData, "Stress level"@en .

:trailMakingTest
    a owl:DatatypeProperty ;
    rdfs:comment "Trail making test A and B"@en ;
    rdfs:domain :CognitiveAbilitiesAssessment ;
    rdfs:seeAlso :CognitiveAbilitiesAssessment .

:verbalReactionSpontaneity
    a owl:DatatypeProperty ;
    rdfs:comment "Time between the end of assessor’s speech and the beginning of participant’s speech (Discussion Task)"@en ;
    rdfs:domain :DiscussionInvolvementData ;
    rdfs:seeAlso :DiscussionInvolvementData .

:verbalReactionTime
    a owl:DatatypeProperty ;
    rdfs:comment "Time interval between the end of assessor’s speech and the beginning of participant’s speech (Sentence Repetition Task)"@en ;
    rdfs:domain :VoiceData ;
    rdfs:seeAlso :VoiceData .

:vowelDuration
    a owl:DatatypeProperty ;
    rdfs:comment :VoiceData ;
    rdfs:domain :VoiceData ;
    rdfs:seeAlso "Vowel duration"@en ;
    rdfs:subPropertyOf owl:topDataProperty .

:walkingSpeed
    a owl:DatatypeProperty ;
    rdfs:comment "Walking speed"@en ;
    rdfs:domain :GaitData ;
    rdfs:seeAlso :GaitData .

:walkingSpeedInstantaneous
    a owl:DatatypeProperty ;
    rdfs:comment "Walking speed instantaneous (mono task walking activity/dual task)"@en ;
    rdfs:domain :GaitData ;
    rdfs:seeAlso :GaitData .

<http://www.iti.gr/iti/people/Georgios_Meditskos.html#gmeditsk>
    a foaf:Person ;
    foaf:homepage <http://www.iti.gr/iti/people/Georgios_Meditskos.html> ;
    foaf:name "Georgios Meditskos" .

<http://www.iti.gr/iti/people/Ioannis_Kompatsiaris.html#ikom>
    a foaf:Person ;
    foaf:homepage <http://www.iti.gr/iti/people/Ioannis_Kompatsiaris.html> ;
    foaf:name "Ioannis Kompatsiaris" .

xsd:date
    a rdfs:Datatype .

skos:note
    a owl:AnnotationProperty .

