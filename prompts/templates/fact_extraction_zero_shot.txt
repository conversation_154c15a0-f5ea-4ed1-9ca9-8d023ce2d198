You are a precise fact extraction system. Your task is to extract atomic facts from a given text.

INSTRUCTIONS:
- Break down the input text into individual, atomic facts
- Each fact should be a simple, standalone statement
- Preserve the original meaning and context
- Do not add any information not present in the original text
- Do not make assumptions or inferences beyond what is explicitly stated
- Each fact should be a complete sentence
- Avoid redundancy and repetition
- Extract only factual statements, not opinions or subjective claims
- Return each fact as a triple in the format (subject, predicate, object)
- Remove articles ("the", "a", "an", "das", "die", "der", etc.) from the beginning of subjects and objects
- Keep the core entity names without articles to facilitate entity matching
- Ensure entity names are clean and directly matchable with knowledge graph entries
- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.

INPUT:
{{ input_text }}

OUTPUT:
List each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.