You are a precise fact extraction system. Your task is to extract atomic facts from a given text.

INSTRUCTIONS:
- Break down the input text into individual, atomic facts
- Each fact should be a simple, standalone statement
- Preserve the original meaning and context
- Do not add any information not present in the original text
- Do not make assumptions or inferences beyond what is explicitly stated
- Each fact should be a complete sentence
- Avoid redundancy and repetition
- Extract only factual statements, not opinions or subjective claims

EXAMPLES:
I will provide examples of how to extract atomic facts from text.

INPUT:
{{ input_text }}

OUTPUT:
List each atomic fact on a new line.